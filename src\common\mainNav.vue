<script setup>
// 当前时间
const currentTime = ref(new Date());
const inited = ref(false);

// 格式化时间显示
const formatTime = date => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 格式化日期显示
const formatDate = date => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}年${month}月${day}日`;
};

// 格式化时间显示
const formatTimeOnly = date => {
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${hours}:${minutes}:${seconds}`;
};

// 获取星期
const getWeekDay = date => {
  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  return weekDays[date.getDay()];
};

let timer = null;

onMounted(() => {
  inited.value = true;

  // 启动时间更新定时器
  timer = setInterval(() => {
    currentTime.value = new Date();
  }, 1000);
});

onUnmounted(() => {
  // 清理定时器
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>
<template>
  <div :class="`nav-wrap ${inited && 'inited'}`">
    <div class="time-display">
      <div class="date-info">
        <span class="date">{{ formatDate(currentTime) }}</span>
        <span class="weekday">{{ getWeekDay(currentTime) }}</span>
      </div>
      <div class="time-info">
        <span class="time">{{ formatTimeOnly(currentTime) }}</span>
      </div>
    </div>
  </div>
</template>
<style lang="less">
.nav-wrap {
  .poa;
  right: -10px;
  top: 20px;
  z-index: 20;

  .time-display {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
    padding: 12px 20px;

    .date-info {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 12px;

      .date {
        font-size: 16px;
        font-weight: 500;
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      }

      .weekday {
        font-size: 14px;
        color: #cccccc;
        opacity: 0.8;
      }
    }

    .time-info {
      .time {
        font-size: 20px;
        font-weight: 600;
        color: #00d4ff;
        text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        font-family: 'Courier New', monospace;
        letter-spacing: 1px;
      }
    }
  }

  &.inited {
    .time-display {
      animation: fadeInSlide 0.8s ease-out;
    }
  }
}

@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
