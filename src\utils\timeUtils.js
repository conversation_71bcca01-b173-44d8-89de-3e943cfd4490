/**
 * 时间工具类
 * 提供常用的时间处理功能
 */

/**
 * 格式化时间
 * @param {Date|string|number} date - 时间对象、时间字符串或时间戳
 * @param {string} format - 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  const milliseconds = String(d.getMilliseconds()).padStart(3, '0');

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
    .replace('SSS', milliseconds);
}

/**
 * 获取相对时间描述
 * @param {Date|string|number} date - 时间
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date) {
  if (!date) return '';
  
  const now = new Date();
  const target = new Date(date);
  const diff = now.getTime() - target.getTime();
  
  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;
  const year = 365 * day;

  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`;
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`;
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`;
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`;
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`;
  } else {
    return `${Math.floor(diff / year)}年前`;
  }
}

/**
 * 获取时间段
 * @param {Date|string|number} date - 时间
 * @returns {string} 时间段描述
 */
export function getTimePeriod(date = new Date()) {
  const hour = new Date(date).getHours();
  
  if (hour >= 5 && hour < 12) {
    return '上午';
  } else if (hour >= 12 && hour < 18) {
    return '下午';
  } else if (hour >= 18 && hour < 22) {
    return '晚上';
  } else {
    return '深夜';
  }
}

/**
 * 获取问候语
 * @param {Date|string|number} date - 时间
 * @returns {string} 问候语
 */
export function getGreeting(date = new Date()) {
  const hour = new Date(date).getHours();
  
  if (hour >= 5 && hour < 12) {
    return '早上好';
  } else if (hour >= 12 && hour < 14) {
    return '中午好';
  } else if (hour >= 14 && hour < 18) {
    return '下午好';
  } else if (hour >= 18 && hour < 22) {
    return '晚上好';
  } else {
    return '夜深了';
  }
}

/**
 * 计算两个时间的差值
 * @param {Date|string|number} startDate - 开始时间
 * @param {Date|string|number} endDate - 结束时间
 * @returns {Object} 时间差对象
 */
export function getTimeDiff(startDate, endDate = new Date()) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diff = Math.abs(end.getTime() - start.getTime());

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  return {
    days,
    hours,
    minutes,
    seconds,
    totalMilliseconds: diff,
    totalSeconds: Math.floor(diff / 1000),
    totalMinutes: Math.floor(diff / (1000 * 60)),
    totalHours: Math.floor(diff / (1000 * 60 * 60)),
    totalDays: Math.floor(diff / (1000 * 60 * 60 * 24))
  };
}

/**
 * 判断是否为今天
 * @param {Date|string|number} date - 时间
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  const today = new Date();
  const target = new Date(date);
  
  return today.getFullYear() === target.getFullYear() &&
         today.getMonth() === target.getMonth() &&
         today.getDate() === target.getDate();
}

/**
 * 判断是否为昨天
 * @param {Date|string|number} date - 时间
 * @returns {boolean} 是否为昨天
 */
export function isYesterday(date) {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const target = new Date(date);
  
  return yesterday.getFullYear() === target.getFullYear() &&
         yesterday.getMonth() === target.getMonth() &&
         yesterday.getDate() === target.getDate();
}

/**
 * 判断是否为本周
 * @param {Date|string|number} date - 时间
 * @returns {boolean} 是否为本周
 */
export function isThisWeek(date) {
  const now = new Date();
  const target = new Date(date);
  
  // 获取本周一的日期
  const monday = new Date(now);
  monday.setDate(now.getDate() - now.getDay() + 1);
  monday.setHours(0, 0, 0, 0);
  
  // 获取本周日的日期
  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);
  
  return target >= monday && target <= sunday;
}

/**
 * 获取本月第一天
 * @param {Date|string|number} date - 时间
 * @returns {Date} 本月第一天
 */
export function getFirstDayOfMonth(date = new Date()) {
  const d = new Date(date);
  return new Date(d.getFullYear(), d.getMonth(), 1);
}

/**
 * 获取本月最后一天
 * @param {Date|string|number} date - 时间
 * @returns {Date} 本月最后一天
 */
export function getLastDayOfMonth(date = new Date()) {
  const d = new Date(date);
  return new Date(d.getFullYear(), d.getMonth() + 1, 0);
}

/**
 * 获取本周第一天（周一）
 * @param {Date|string|number} date - 时间
 * @returns {Date} 本周第一天
 */
export function getFirstDayOfWeek(date = new Date()) {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整周日
  return new Date(d.setDate(diff));
}

/**
 * 获取本周最后一天（周日）
 * @param {Date|string|number} date - 时间
 * @returns {Date} 本周最后一天
 */
export function getLastDayOfWeek(date = new Date()) {
  const firstDay = getFirstDayOfWeek(date);
  const lastDay = new Date(firstDay);
  lastDay.setDate(firstDay.getDate() + 6);
  return lastDay;
}

/**
 * 添加时间
 * @param {Date|string|number} date - 基础时间
 * @param {number} amount - 数量
 * @param {string} unit - 单位 (years, months, days, hours, minutes, seconds)
 * @returns {Date} 新的时间对象
 */
export function addTime(date, amount, unit) {
  const d = new Date(date);
  
  switch (unit) {
    case 'years':
      d.setFullYear(d.getFullYear() + amount);
      break;
    case 'months':
      d.setMonth(d.getMonth() + amount);
      break;
    case 'days':
      d.setDate(d.getDate() + amount);
      break;
    case 'hours':
      d.setHours(d.getHours() + amount);
      break;
    case 'minutes':
      d.setMinutes(d.getMinutes() + amount);
      break;
    case 'seconds':
      d.setSeconds(d.getSeconds() + amount);
      break;
    default:
      throw new Error('Invalid unit. Use: years, months, days, hours, minutes, seconds');
  }
  
  return d;
}

/**
 * 获取时间戳
 * @param {Date|string|number} date - 时间
 * @param {boolean} inSeconds - 是否返回秒级时间戳
 * @returns {number} 时间戳
 */
export function getTimestamp(date = new Date(), inSeconds = false) {
  const timestamp = new Date(date).getTime();
  return inSeconds ? Math.floor(timestamp / 1000) : timestamp;
}

/**
 * 从时间戳创建日期
 * @param {number} timestamp - 时间戳
 * @param {boolean} inSeconds - 时间戳是否为秒级
 * @returns {Date} 日期对象
 */
export function fromTimestamp(timestamp, inSeconds = false) {
  return new Date(inSeconds ? timestamp * 1000 : timestamp);
}
