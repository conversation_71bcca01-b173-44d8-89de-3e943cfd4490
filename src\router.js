import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
  useRoute,
} from 'vue-router'
import { defineAsyncComponent } from 'vue'
import store from './store'

const router = createRouter({
  // history: createWebHistory(),
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'layout',
      redirect: '/dashboard',
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('@/views/dashboard/index.vue'),
    },
    {
      path: '/dashboard/:scene',
      name: 'dashboard-scene',
      component: () => import('@/views/dashboard/index.vue'),
    },
  ],
})

export default router
