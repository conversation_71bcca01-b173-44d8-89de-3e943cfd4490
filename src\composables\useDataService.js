import { reactive, computed, readonly } from 'vue'
import dashboardData from '@/data/dashboardData.json'

// 全局数据状态
const globalData = reactive({
  ...dashboardData,
  lastUpdateTime: new Date(),
  selectedBaseId: 'huadong', // 默认选中华东基地
})

// 数据服务组合函数
export function useDataService() {
  // 基地相关数据
  const getBaseData = () => globalData.bases

  const getBaseById = (id) => globalData.bases.find((base) => base.id === id)

  const getCurrentBase = () => getBaseById(globalData.selectedBaseId)

  const setSelectedBase = (baseId) => {
    globalData.selectedBaseId = baseId
  }

  // 全国基地总览数据
  const getGlobalOverviewData = () => ({
    arry: [
      {
        title: '全国总产能',
        icon: 'i carbon:factory',
        unit: '吨/月',
        total: globalData.globalOverview.totalCapacity,
      },
      {
        title: '运行基地数',
        icon: 'i carbon:location-filled',
        unit: '个',
        total: globalData.globalOverview.runningBases,
      },
      {
        title: '总订单量',
        icon: 'i carbon:document-multiple',
        unit: '单',
        total: globalData.globalOverview.totalOrders,
      },
      {
        title: '平均效率',
        icon: 'i carbon:chart-line',
        unit: '%',
        total: globalData.globalOverview.averageEfficiency,
      },
    ],
  })

  // 基地效率排名数据
  const getBaseEfficiencyRanking = () => {
    return globalData.bases
      .map((base) => ({
        name: base.name,
        location: base.location,
        efficiency: base.efficiency,
        status:
          base.efficiency >= 90
            ? 'excellent'
            : base.efficiency >= 85
            ? 'good'
            : 'maintenance',
        capacity: parseInt(base.capacity),
        actualOutput: base.actualOutput,
      }))
      .sort((a, b) => b.efficiency - a.efficiency)
  }

  // 基地产能对比数据
  const getBaseComparisonData = () => ({
    bases: globalData.bases.map((base) => base.name),
    capacity: globalData.bases.map((base) => parseInt(base.capacity)),
    actualOutput: globalData.bases.map((base) => base.actualOutput),
    efficiency: globalData.bases.map((base) => base.efficiency),
  })

  // 生产线状态数据
  const getProductionLines = () => globalData.productionLines

  // 当前基地的生产线状态数据
  const getCurrentBaseProductionLines = () => {
    const currentBase = getCurrentBase()
    if (!currentBase || !currentBase.productionLines) {
      return globalData.productionLines || []
    }
    return currentBase.productionLines
  }

  // 原料消耗数据
  const getMaterialsData = () =>
    globalData.materialsData || globalData.materials

  // 订单流向统计数据
  const getOrderFlowStats = () => globalData.orderFlowStats

  // 产品产量统计数据
  const getProductionStatsData = () => globalData.productionStats

  // 当前基地的产品产量统计数据
  const getCurrentBaseProductionStatsData = () => {
    const currentBase = getCurrentBase()
    if (!currentBase || !currentBase.productionStats) {
      return globalData.productionStats
    }
    return currentBase.productionStats
  }

  // 车间雷达图数据
  const getWorkshopRadarData = () =>
    globalData.workshopRadarData || {
      indicators: globalData.workshopRadar.indicators,
      values: [
        {
          name: '当前状态',
          value: globalData.workshopRadar.currentValues,
          itemStyle: { color: '#00d4ff' },
        },
      ],
    }

  // 排产执行总览数据
  const getSchedulingOverviewData = () => ({
    arry: [
      {
        title: '今日排产计划',
        icon: 'i carbon:task-complete',
        unit: '吨',
        total: globalData.schedulingOverview.todayPlan,
      },
      {
        title: '计划完成率',
        icon: 'i carbon:percentage-filled',
        unit: '%',
        total: globalData.schedulingOverview.completionRate,
      },
      {
        title: '在产订单数',
        icon: 'i carbon:document',
        unit: '单',
        total: globalData.schedulingOverview.activeOrders,
      },
      {
        title: '待排产订单',
        icon: 'i carbon:pending',
        unit: '单',
        total: globalData.schedulingOverview.pendingOrders,
      },
    ],
  })

  // 本周生产数据
  const getWeeklyProductionData = () =>
    globalData.weeklyProductionData || globalData.weeklyProduction

  // 人员配置数据
  const getPersonnelData = () => globalData.personnelData

  // 安全指标数据
  const getSafetyData = () => globalData.safetyData

  // 物流数据
  const getLogisticsData = () => globalData.logisticsData

  // 设备状态数据
  const getEquipmentStatusData = () => globalData.equipmentStatus

  // 当前基地的设备状态数据
  const getCurrentBaseEquipmentStatusData = () => {
    const currentBase = getCurrentBase()
    if (!currentBase || !currentBase.equipmentStatus) {
      return globalData.equipmentStatus
    }
    return currentBase.equipmentStatus
  }

  // 质量趋势数据
  const getQualityTrendData = () => globalData.qualityTrend

  // 当前基地的质量趋势数据
  const getCurrentBaseQualityTrendData = () => {
    const currentBase = getCurrentBase()
    if (!currentBase || !currentBase.qualityTrend) {
      return globalData.qualityTrend
    }
    return currentBase.qualityTrend
  }

  // 订单飞线数据
  const getOrderFlightsData = () => globalData.orderFlights

  // 当前基地的订单飞线数据（只显示当前基地的订单）
  const getCurrentBaseOrderFlights = () => {
    const currentBase = getCurrentBase()
    if (!currentBase) return []

    return globalData.orderFlights.filter(
      (flight) => flight.fromName === currentBase.name,
    )
  }

  // 24小时产量监控数据
  const getHourlyProductionData = () => globalData.hourlyProduction

  // 当前基地的24小时产量监控数据
  const getCurrentBaseHourlyProductionData = () => {
    const currentBase = getCurrentBase()
    if (!currentBase || !currentBase.hourlyProduction) {
      return globalData.hourlyProduction
    }
    return currentBase.hourlyProduction
  }

  // 能耗数据
  const getEnergyData = () => globalData.energyData

  // 订单管理数据
  const getOrderManagementData = () => globalData.orderManagement

  // 甘特图任务数据
  const getGanttTasksData = () => globalData.ganttTasks

  // 生产表格数据
  const getProductionTableData = () => globalData.productionTableData

  // 生产右侧面板数据
  const getProductionRightData = () => globalData.productionRightData

  // 周生产时长数据
  const getWeeklyProductionTimeData = () =>
    globalData.productionRightData?.weeklyProductionTime

  // 地图数据（用于地图组件）
  const getMapData = () => ({
    baseData: globalData.bases.map((base) => ({
      name: base.name,
      value: [...base.coordinates, parseInt(base.capacity) || 0],
      location: base.location,
      capacity: base.capacity,
      status: base.status,
      employees: base.employees,
      description: base.description,
      id: base.id,
    })),
  })

  // 当前基地的地图数据（只显示选中的基地）
  const getCurrentBaseMapData = () => {
    const currentBase = getCurrentBase()
    if (!currentBase) return { baseData: [] }

    return {
      baseData: [
        {
          name: currentBase.name,
          value: [
            ...currentBase.coordinates,
            parseInt(currentBase.capacity) || 0,
          ],
          location: currentBase.location,
          capacity: currentBase.capacity,
          status: currentBase.status,
          employees: currentBase.employees,
          description: currentBase.description,
          id: currentBase.id,
        },
      ],
    }
  }

  // 更新数据的方法
  const updateData = (newData) => {
    Object.assign(globalData, newData)
    globalData.lastUpdateTime = new Date()
  }

  // 获取最后更新时间
  const getLastUpdateTime = () => globalData.lastUpdateTime

  return {
    // 数据获取方法
    getBaseData,
    getBaseById,
    getCurrentBase,
    setSelectedBase,
    getGlobalOverviewData,
    getBaseEfficiencyRanking,
    getBaseComparisonData,
    getProductionLines,
    getCurrentBaseProductionLines,
    getMaterialsData,
    getOrderFlowStats,
    getProductionStatsData,
    getCurrentBaseProductionStatsData,
    getWorkshopRadarData,
    getSchedulingOverviewData,
    getWeeklyProductionData,
    getPersonnelData,
    getSafetyData,
    getLogisticsData,
    getEquipmentStatusData,
    getCurrentBaseEquipmentStatusData,
    getQualityTrendData,
    getCurrentBaseQualityTrendData,
    getOrderFlightsData,
    getCurrentBaseOrderFlights,
    getHourlyProductionData,
    getCurrentBaseHourlyProductionData,
    getEnergyData,
    getOrderManagementData,
    getGanttTasksData,
    getProductionTableData,
    getProductionRightData,
    getWeeklyProductionTimeData,
    getMapData,
    getCurrentBaseMapData,

    // 数据管理方法
    updateData,
    getLastUpdateTime,

    // 响应式数据
    globalData: readonly(globalData),
  }
}

// 默认导出，方便直接使用
export default useDataService
