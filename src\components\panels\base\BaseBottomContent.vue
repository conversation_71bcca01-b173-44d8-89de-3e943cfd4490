<template>
  <div class="bottom-content">
    <!-- 左侧：生产线状态监控 -->
    <div class="bottom-left">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig1">
          <i class="i carbon:assembly-cluster icon"></i>
        </decoFrameB2>
        <span class="title">生产线状态监控</span>
      </div>

      <div class="content-card">
        <div class="production-lines-grid">
          <div
            class="line-card"
            v-for="line in productionLines"
            :key="line.id"
            :class="line.status"
          >
            <div class="line-header">
              <span class="line-name">{{ line.name }}</span>
              <div class="status-indicator" :class="line.status">
                <span class="status-dot"></span>
                <span class="status-text">{{ line.statusText }}</span>
              </div>
            </div>
            <div class="line-metrics">
              <div class="metric">
                <span class="label">产量</span>
                <span class="value">{{ line.output }}吨</span>
              </div>
              <div class="metric">
                <span class="label">效率</span>
                <span class="value">{{ line.efficiency }}%</span>
              </div>
              <div class="metric">
                <span class="label">良品率</span>
                <span class="value">{{ line.quality }}%</span>
              </div>
            </div>
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: line.efficiency + '%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间：设备运行状态 -->
    <div class="bottom-center">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig1">
          <i class="i carbon:machine-learning-model icon"></i>
        </decoFrameB2>
        <span class="title">关键设备状态</span>
      </div>

      <div class="content-card">
        <div class="equipment-status-wrapper">
          <echartsInit :chartOption="equipmentState.option" />
        </div>
      </div>
    </div>

    <!-- 右侧：质量指标趋势 -->
    <div class="bottom-right">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig1">
          <i class="i carbon:chart-line-smooth icon"></i>
        </decoFrameB2>
        <span class="title">质量指标趋势</span>
      </div>

      <div class="content-card">
        <div class="quality-chart-wrapper">
          <echartsInit :chartOption="qualityState.option" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

// decoFrameB2 配置
const decoFrameConfig1 = {
  directionAlt: true,
  scale: 0.6,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 36,
}

/* =================== 生产线状态数据 - 使用数据服务 =================== */
const productionLines = reactive(dataService.getCurrentBaseProductionLines())

/* =================== 设备运行状态图表 =================== */
const equipmentState = reactive({ option: {} })

const buildEquipmentOption = () => {
  // 使用数据服务获取当前基地的设备状态数据
  const equipmentData = dataService.getCurrentBaseEquipmentStatusData()

  equipmentState.option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      textStyle: { color: '#ffffff' },
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%',
      textStyle: { color: '#aaddff', fontSize: 12 },
      itemWidth: 12,
      itemHeight: 8,
    },
    series: [
      {
        name: '设备状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c}台',
          color: '#aaddff',
          fontSize: 11,
        },
        labelLine: {
          show: true,
          lineStyle: { color: '#00d4ff' },
        },
        data: equipmentData.map((item) => ({
          name: item.name,
          value: item.value,
          itemStyle: { color: item.color },
        })),
      },
    ],
  }
}

/* =================== 质量指标趋势图表 =================== */
const qualityState = reactive({ option: {} })

const buildQualityOption = () => {
  // 使用数据服务获取当前基地的质量趋势数据
  const qualityTrendData = dataService.getCurrentBaseQualityTrendData()
  const hours = qualityTrendData.hours
  const qualityRate = qualityTrendData.qualityRate
  const defectRate = qualityTrendData.defectRate

  qualityState.option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      textStyle: { color: '#ffffff' },
    },
    legend: {
      data: ['良品率', '缺陷率'],
      textStyle: {
        color: '#aaddff',
        fontSize: 12,
        fontWeight: 'normal',
      },
      top: 5,
      itemGap: 20,
    },
    grid: { left: 15, right: 15, top: 35, bottom: 25, containLabel: true },
    xAxis: {
      type: 'category',
      data: hours,
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#aaddff', fontSize: 10 },
    },
    yAxis: [
      {
        type: 'value',
        name: '良品率(%)',
        nameTextStyle: { color: '#aaddff', fontSize: 11 },
        position: 'left',
        axisLine: { show: false },
        splitLine: { lineStyle: { color: 'rgba(0,212,255,0.1)' } },
        axisLabel: { color: '#aaddff', fontSize: 10 },
        min: 95,
        max: 100,
      },
      {
        type: 'value',
        name: '缺陷率(%)',
        nameTextStyle: { color: '#aaddff', fontSize: 11 },
        position: 'right',
        axisLine: { show: false },
        axisLabel: { color: '#aaddff', fontSize: 10 },
        min: 0,
        max: 5,
      },
    ],
    series: [
      {
        name: '良品率',
        type: 'line',
        yAxisIndex: 0,
        smooth: true,
        data: qualityRate,
        itemStyle: { color: '#00d4ff' },
        lineStyle: { width: 2 },
        symbol: 'circle',
        symbolSize: 6,
      },
      {
        name: '缺陷率',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        data: defectRate,
        itemStyle: { color: '#ff6b6b' },
        lineStyle: { width: 2 },
        symbol: 'circle',
        symbolSize: 6,
      },
    ],
  }
}

onMounted(() => {
  buildEquipmentOption()
  buildQualityOption()
})

// 监听选中基地的变化，重新构建图表和数据
watch(
  () => dataService.globalData.selectedBaseId,
  () => {
    // 重新获取生产线数据
    Object.assign(productionLines, dataService.getCurrentBaseProductionLines())

    // 重新构建图表选项
    buildEquipmentOption()
    buildQualityOption()
  },
  { immediate: false },
)
</script>

<style lang="less" scoped>
.bottom-content {
  display: flex;
  gap: 12px;

  .bottom-left {
    flex: 2.5;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .bottom-center {
    flex: 1.5;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .bottom-right {
    flex: 2;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .data-title {
    display: flex;
    align-items: center;
    height: 36px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.15),
      rgba(0, 212, 255, 0.05)
    );
    backdrop-filter: blur(10px);

    .icon {
      font-size: 45px;
      color: #00d4ff;
      filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
    }

    .title {
      color: #75d1f0;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: 0.5px;
      margin-left: -8px;
    }
  }

  /* 通用内容卡片样式 */
  .content-card {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.06),
      rgba(0, 212, 255, 0.03)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 14px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 14px rgba(0, 212, 255, 0.08);
    flex: 1;
  }

  /* 生产线状态网格 */
  .production-lines-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    height: 100%;
  }

  .line-card {
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.15);
    border-radius: 8px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    transition: all 0.3s ease;

    &.running {
      border-color: rgba(0, 212, 255, 0.3);
      box-shadow: 0 0 8px rgba(0, 212, 255, 0.1);
    }

    &.warning {
      border-color: rgba(255, 170, 0, 0.3);
      background: rgba(255, 170, 0, 0.05);
    }

    &.maintenance {
      border-color: rgba(102, 102, 102, 0.3);
      background: rgba(102, 102, 102, 0.05);
    }

    .line-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .line-name {
        font-size: 13px;
        font-weight: 600;
        color: #00d4ff;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 4px;

        .status-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #00d4ff;
        }

        &.warning .status-dot {
          background: #ffaa00;
        }

        &.maintenance .status-dot {
          background: #666666;
        }

        .status-text {
          font-size: 10px;
          color: #aaddff;
        }
      }
    }

    .line-metrics {
      display: flex;
      justify-content: space-between;
      gap: 4px;

      .metric {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;

        .label {
          font-size: 9px;
          color: #99ccff;
          margin-bottom: 2px;
        }

        .value {
          font-size: 11px;
          font-weight: 600;
          color: #00d4ff;
        }
      }
    }

    .progress-bar {
      height: 3px;
      background: rgba(0, 212, 255, 0.1);
      border-radius: 2px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #00d4ff, #66ccff);
        border-radius: 2px;
        transition: width 0.3s ease;
      }
    }
  }

  /* 设备状态图表容器 */
  .equipment-status-wrapper {
    flex: 1;
    height: 100%;

    > * {
      width: 100% !important;
      height: 100% !important;
    }
  }

  /* 质量指标图表容器 */
  .quality-chart-wrapper {
    flex: 1;
    height: 100%;

    > * {
      width: 100% !important;
      height: 100% !important;
    }

    /* 确保ECharts图例文字颜色正确 */
    :deep(.echarts-legend) {
      color: #aaddff !important;
    }

    :deep(.echarts-legend-item) {
      color: #aaddff !important;
    }
  }
}
</style>
