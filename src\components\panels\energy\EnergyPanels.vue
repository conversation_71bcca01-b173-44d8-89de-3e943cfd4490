<template>
  <div class="energy-panels">
    <EnergyRightContent v-if="panelType === 'right'" />
    <EnergyBottomContent v-if="panelType === 'bottom'" />
  </div>
</template>

<script setup>
import EnergyRightContent from './EnergyRightContent.vue'
import EnergyBottomContent from './EnergyBottomContent.vue'

const props = defineProps({
  panelType: {
    type: String,
    required: true,
    validator: (value) => ['right', 'bottom'].includes(value),
  },
})
</script>

<style lang="less" scoped>
.energy-panels {
  height: 100%;
  padding: 10px;
}
</style>
