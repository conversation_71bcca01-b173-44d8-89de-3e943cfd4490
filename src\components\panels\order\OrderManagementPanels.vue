<template>
  <div class="order-management-panels">
    <OrderManagementRightContent v-if="panelType === 'right'" />
    <OrderManagementBottomContent v-if="panelType === 'bottom'" />
  </div>
</template>

<script setup>
import OrderManagementRightContent from './OrderManagementRightContent.vue'
import OrderManagementBottomContent from './OrderManagementBottomContent.vue'

const props = defineProps({
  panelType: {
    type: String,
    required: true,
    validator: (value) => ['right', 'bottom'].includes(value),
  },
})
</script>

<style lang="less" scoped>
.order-management-panels {
  height: 100%;
}
</style>
