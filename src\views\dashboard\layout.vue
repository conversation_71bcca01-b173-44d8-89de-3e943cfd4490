<script setup>
import { reactive, computed, ref, nextTick, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import mainNav from '@/common/mainNav.vue'
import ButtonList from '@/components/ButtonList.vue'
import IndexPanels from '@/components/panels/index/IndexPanels.vue'
import BasePanels from '@/components/panels/base/BasePanels.vue'
import OverviewPanels from '@/components/panels/overview/OverviewPanels.vue'
import ProductionPanels from '@/components/panels/production/ProductionPanels.vue'
import EnergyPanels from '@/components/panels/energy/EnergyPanels.vue'
import OrderManagementPanels from '@/components/panels/order/OrderManagementPanels.vue'
import Background3DScene from '@/components/Background3DScene.vue'
import ChinaMapScene from '@/components/ChinaMapScene.vue'
import FactoryExteriorScene from '@/components/FactoryExteriorScene.vue'
import { useDataService } from '@/composables/useDataService'

// 路由相关
const route = useRoute()
const router = useRouter()

// 数据服务
const dataService = useDataService()

const state = reactive({
  titleConfig: {
    text: '锂电铜箔排产可视化系统',
  },
  activeButtonId: 'overview', // 默认激活第一个按钮
  currentScene: 'map', // 当前场景：'map'、'factory' 或 'workshop'
  selectedBase: null, // 选中的基地信息
  selectedBuilding: null, // 选中的建筑信息
})

// 计算当前激活的面板组件 - 根据场景显示不同面板
const currentPanelComponent = computed(() => {
  // 地图场景：显示首页面板
  if (state.currentScene === 'map') {
    return IndexPanels
  }

  // 工厂外景场景：显示基地面板
  if (state.currentScene === 'factory') {
    return BasePanels
  }

  // 车间场景：根据导航按钮显示对应面板
  const componentMap = {
    index: IndexPanels,
    overview: OverviewPanels,
    production: ProductionPanels,
    energy: EnergyPanels,
    emergency: OrderManagementPanels,
  }
  return componentMap[state.activeButtonId] || OverviewPanels
})

// 处理按钮点击事件
const handleNavButtonClick = (button) => {
  console.log('导航按钮点击:', button)
  state.activeButtonId = button.id // 更新激活状态
}

// 处理进入工厂外景场景事件
const handleEnterFactoryScene = (baseInfo) => {
  console.log('进入工厂外景场景，基地信息:', baseInfo)
  state.selectedBase = baseInfo
  state.currentScene = 'factory'

  // 设置数据服务中的选中基地ID
  if (baseInfo && baseInfo.id) {
    dataService.setSelectedBase(baseInfo.id)
    console.log('设置选中基地ID:', baseInfo.id)
  }

  // 更新URL
  updateURL('factory')
}

// 处理进入车间场景事件
const handleEnterWorkshop = (buildingInfo) => {
  console.log('进入车间场景，建筑信息:', buildingInfo)
  state.selectedBuilding = buildingInfo
  state.currentScene = 'workshop'

  // 更新URL
  updateURL('workshop')
}

// 返回地图场景
const backToMapScene = () => {
  console.log('返回地图场景')
  state.currentScene = 'map'
  state.selectedBase = null
  state.selectedBuilding = null
  state.titleConfig.text = '锂电铜箔排产可视化系统'

  // 更新URL
  updateURL('map')
}

// 返回工厂外景场景
const backToFactoryScene = () => {
  console.log('返回工厂外景场景')
  state.currentScene = 'factory'
  state.selectedBuilding = null

  // 恢复工厂外景标题
  state.titleConfig.text = `锂电铜箔排产可视化系统`

  // 更新URL
  updateURL('factory')
}

// 根据URL参数初始化场景
const initializeSceneFromURL = () => {
  const sceneParam = route.params.scene
  console.log('URL场景参数:', sceneParam)

  if (sceneParam) {
    const validScenes = ['map', 'factory', 'workshop']
    if (validScenes.includes(sceneParam)) {
      state.currentScene = sceneParam
      console.log('从URL初始化场景:', sceneParam)

      // 根据场景设置默认数据
      if (sceneParam === 'factory') {
        // 工厂场景需要选择一个默认基地
        state.selectedBase = {
          name: '华东基地',
          location: '上海市',
          capacity: '3500吨/月',
          employees: 280,
          status: 'running',
          description: '主要生产高精度锂电铜箔',
          id: 'huadong',
        }
        // 设置数据服务中的选中基地ID
        dataService.setSelectedBase('huadong')
      } else if (sceneParam === 'workshop') {
        // 车间场景需要设置默认基地和建筑
        state.selectedBase = {
          name: '华东基地',
          location: '上海市',
          capacity: '3500吨/月',
          employees: 280,
          status: 'running',
          description: '主要生产高精度锂电铜箔',
          id: 'huadong',
        }
        // 设置数据服务中的选中基地ID
        dataService.setSelectedBase('huadong')
        state.selectedBuilding = {
          name: '生产车间A',
          type: 'production',
          description: '主要生产线车间',
        }
      }
    }
  }
}

// 更新URL而不刷新页面
const updateURL = (scene) => {
  const newPath = scene === 'map' ? '/dashboard' : `/dashboard/${scene}`
  if (route.path !== newPath) {
    router.replace(newPath)
  }
}

// 组件挂载时初始化场景
onMounted(() => {
  initializeSceneFromURL()
})
</script>
<template>
  <div class="screen">
    <!-- 头部区域 -->
    <header class="header-area">
      <systemTitleA1 :config="state.titleConfig">{{
        state.titleConfig.text
      }}</systemTitleA1>
      <mainNav></mainNav>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧导航栏区域 - 只在车间场景中显示 -->
      <aside class="left-sidebar" v-if="state.currentScene === 'workshop'">
        <ButtonList
          :activeButtonId="state.activeButtonId"
          @button-click="handleNavButtonClick"
        />
      </aside>
      <!-- 中间主图区域 -->
      <div class="center-content">
        <!-- 场景切换容器 -->
        <div class="scene-container">
          <!-- 场景过渡效果 -->
          <transition name="scene-fade" mode="out-in">
            <!-- 中国地图场景 -->
            <ChinaMapScene
              v-if="state.currentScene === 'map'"
              key="map"
              @enter-3d-scene="handleEnterFactoryScene"
            />

            <!-- 工厂外景场景 -->
            <FactoryExteriorScene
              v-else-if="state.currentScene === 'factory'"
              key="factory"
              :selectedBase="state.selectedBase"
              @enter-workshop="handleEnterWorkshop"
              @back-to-map="backToMapScene"
            />

            <!-- 3D车间场景 -->
            <Background3DScene
              v-else-if="state.currentScene === 'workshop'"
              key="workshop"
            />
          </transition>

          <!-- 返回按钮 - 根据当前场景显示不同的返回选项 -->
          <div
            v-if="state.currentScene === 'factory'"
            class="back-to-map-btn"
            @click="backToMapScene"
          >
            <i class="i carbon:arrow-left"></i>
            <span>返回基地分布</span>
          </div>

          <div
            v-if="state.currentScene === 'workshop'"
            class="back-to-factory-btn"
            @click="backToFactoryScene"
          >
            <i class="i carbon:arrow-left"></i>
            <span>返回工厂外景</span>
          </div>

          <!-- 当前基地信息显示 -->
          <div
            v-if="
              (state.currentScene === 'factory' ||
                state.currentScene === 'workshop') &&
              state.selectedBase
            "
            class="current-base-info"
          >
            <div class="base-badge">
              <i class="i carbon:location"></i>
              <span>{{ state.selectedBase.name }}</span>
              <span class="base-status" :class="state.selectedBase.status">
                {{
                  state.selectedBase.status === 'running' ? '运行中' : '维护中'
                }}
              </span>
              <span
                v-if="state.currentScene === 'workshop'"
                class="scene-indicator"
              >
                车间内部
              </span>
              <span
                v-else-if="state.currentScene === 'factory'"
                class="scene-indicator"
              >
                工厂外景
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板区域 -->
      <div class="right-panel">
        <component
          :is="currentPanelComponent"
          panel-type="right"
          v-if="currentPanelComponent"
        />
      </div>

      <!-- 下方面板区域 -->
      <div class="bottom-panel">
        <component
          :is="currentPanelComponent"
          panel-type="bottom"
          v-if="currentPanelComponent"
        />
      </div>
    </div>
  </div>
</template>
<style lang="less">
.screen {
  position: relative;
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100%;
  padding: 0 10px;
  gap: 10px;

  .header-area {
    position: relative;
    z-index: 100;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .main-content {
    display: grid;
    grid-template-columns: minmax(0, 1fr) min-content;
    grid-template-rows: 1fr 280px;
    gap: 10px;
    height: 100%;
    position: relative;
    overflow: hidden;
    padding: 10px;

    // 左侧导航栏区域 - 悬浮透明
    .left-sidebar {
      position: absolute;
      top: 20px;
      bottom: 20px;
      width: 220px;
      z-index: 10;
      display: flex;
      flex-direction: column;
      padding-top: 10px;
      max-height: max-content;
    }

    // 中央主要内容区域
    .center-content {
      grid-column: 1;
      grid-row: 1;
      border-radius: 8px;
      position: relative;
      z-index: 5;
      min-height: 0;
      max-height: 100%;
      overflow: hidden;

      .scene-container {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;

        /* 场景切换过渡效果 */
        .scene-fade-enter-active,
        .scene-fade-leave-active {
          transition: all 0.6s ease-in-out;
        }

        .scene-fade-enter-from {
          opacity: 0;
          transform: translateX(30px) scale(0.95);
        }

        .scene-fade-leave-to {
          opacity: 0;
          transform: translateX(-30px) scale(0.95);
        }

        .scene-fade-enter-to,
        .scene-fade-leave-from {
          opacity: 1;
          transform: translateX(0) scale(1);
        }

        .back-to-map-btn,
        .back-to-factory-btn {
          position: absolute;
          bottom: 20px;
          left: 20px;
          z-index: 20;
          background: linear-gradient(
            135deg,
            rgba(0, 212, 255, 0.9),
            rgba(0, 153, 204, 0.9)
          );
          border: 1px solid rgba(0, 212, 255, 0.5);
          border-radius: 8px;
          padding: 10px 16px;
          color: white;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          backdrop-filter: blur(10px);
          box-shadow: 0 4px 16px rgba(0, 212, 255, 0.3);
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(
              135deg,
              rgba(0, 212, 255, 1),
              rgba(0, 153, 204, 1)
            );
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
          }

          &:active {
            transform: translateY(0);
          }

          i {
            font-size: 16px;
          }
        }

        .current-base-info {
          position: absolute;
          top: 20px;
          right: 20px;
          z-index: 20;

          .base-badge {
            background: linear-gradient(
              135deg,
              rgba(0, 212, 255, 0.15),
              rgba(0, 153, 204, 0.15)
            );
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 10px 16px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 16px rgba(0, 212, 255, 0.2);

            i {
              color: #00d4ff;
              font-size: 16px;
            }

            .base-status {
              padding: 2px 8px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 600;

              &.running {
                background: rgba(76, 175, 80, 0.2);
                color: #4caf50;
                border: 1px solid rgba(76, 175, 80, 0.3);
              }

              &.maintenance {
                background: rgba(255, 174, 0, 0.2);
                color: #ffae00;
                border: 1px solid rgba(255, 174, 0, 0.3);
              }
            }

            .scene-indicator {
              padding: 2px 8px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 600;
              background: rgba(0, 212, 255, 0.2);
              color: #00d4ff;
              border: 1px solid rgba(0, 212, 255, 0.3);
              margin-left: 4px;
            }
          }
        }
      }
    }

    // 右侧面板区域（占据整个右侧高度）
    .right-panel {
      grid-column: 2;
      grid-row: 1 / 3;
      background: rgba(128, 128, 128, 0.15); // 降低透明度
      border-radius: 8px;
      overflow: hidden;
      width: 450px;
      min-width: 450px;
      flex-shrink: 0;
      backdrop-filter: blur(3px); // 添加模糊效果
    }

    // 下方面板区域（只占据左侧列）
    .bottom-panel {
      grid-column: 1;
      grid-row: 2;
      padding: 10px;
      background: rgba(128, 128, 128, 0.15); // 降低透明度
      border-radius: 8px;
      height: 100%;
      min-height: 280px; // 设置最小高度确保内容显示完整
      overflow-y: auto; // 添加垂直滚动
      overflow-x: hidden; // 隐藏水平滚动
      backdrop-filter: blur(3px); // 添加模糊效果

      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }

      scrollbar-width: none; // Firefox
      -ms-overflow-style: none; // IE/Edge
    }
  }
}
</style>
