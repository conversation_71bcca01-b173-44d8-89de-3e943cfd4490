<template>
  <div class="bottom-content">
    <!-- 左侧区域 - 基地人员配置 -->
    <div class="bottom-left">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig1">
          <i class="i carbon:user-multiple icon"></i>
        </decoFrameB2>
        <span class="title">基地人员配置</span>
      </div>

      <!-- 人员配置卡片 -->
      <div class="content-card personnel-card">
        <div class="personnel-chart-wrapper">
          <echartsInit :chartOption="personnelState.option" />
        </div>
      </div>
    </div>

    <!-- 中间区域 - 基地安全指标 -->
    <div class="bottom-center">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig1">
          <i class="i carbon:security icon"></i>
        </decoFrameB2>
        <span class="title">基地安全指标</span>
      </div>
      <div class="content-card safety-card">
        <div class="safety-chart-wrapper">
          <echartsInit :chartOption="safetyState.option" />
        </div>
      </div>
    </div>

    <!-- 右侧区域 - 基地物流运输 -->
    <div class="bottom-right">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig1">
          <i class="i carbon:delivery-truck icon"></i>
        </decoFrameB2>
        <span class="title">基地物流运输</span>
      </div>
      <div class="content-card logistics-card">
        <div class="logistics-chart-wrapper">
          <echartsInit :chartOption="logisticsState.option" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

// decoFrameB2 配置
const decoFrameConfig1 = {
  directionAlt: true,
  scale: 0.6,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 36,
}

/* =================== 基地人员配置数据 - 使用数据服务 =================== */
const personnelData = dataService.getPersonnelData()

/* =================== 基地人员配置图表 =================== */
const personnelState = reactive({ option: {} })

const buildPersonnelOption = () => {
  personnelState.option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const dataIndex = params[0].dataIndex
        const data = personnelData[dataIndex]
        return `${data.baseName}<br/>
                总人数: ${data.totalStaff}人<br/>
                在岗: ${data.onDuty}人<br/>
                技师: ${data.technicians}人<br/>
                出勤率: ${data.attendanceRate}%`
      },
    },
    legend: {
      data: ['总人数', '在岗人数', '技师人数'],
      textStyle: { color: '#aaddff', fontSize: 10 },
      top: 5,
    },
    grid: { left: 30, right: 10, top: 35, bottom: 25 },
    xAxis: {
      type: 'category',
      data: personnelData.map((item) => item.baseName),
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: {
        color: '#aaddff',
        fontSize: 10,
        interval: 0,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      splitLine: { lineStyle: { color: 'rgba(0,212,255,0.1)' } },
      axisLabel: { color: '#aaddff', fontSize: 10 },
      name: '人数',
      nameTextStyle: { color: '#aaddff' },
    },
    series: [
      {
        name: '总人数',
        type: 'bar',
        data: personnelData.map((item) => item.totalStaff),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00d4ff' },
            { offset: 1, color: 'rgba(0, 212, 255, 0.3)' },
          ]),
        },
      },
      {
        name: '在岗人数',
        type: 'bar',
        data: personnelData.map((item) => item.onDuty),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00ff88' },
            { offset: 1, color: 'rgba(0, 255, 136, 0.3)' },
          ]),
        },
      },
      {
        name: '技师人数',
        type: 'bar',
        data: personnelData.map((item) => item.technicians),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffae00' },
            { offset: 1, color: 'rgba(255, 174, 0, 0.3)' },
          ]),
        },
      },
    ],
  }
}

/* =================== 基地安全指标图表 =================== */
const safetyState = reactive({ option: {} })

const buildSafetyOption = () => {
  // 使用数据服务获取安全指标数据
  const safetyData = dataService.getSafetyData()

  safetyState.option = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const data = safetyData[params.dataIndex]
        return `${data.name}<br/>安全评分: ${data.value}分<br/>安全事件: ${data.incidents}起`
      },
    },
    grid: { left: 30, right: 10, top: 35, bottom: 25 },
    xAxis: {
      type: 'category',
      data: safetyData.map((item) => item.name),
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: {
        color: '#aaddff',
        fontSize: 11,
        interval: 0,
        rotate: 0,
      },
    },
    yAxis: {
      type: 'value',
      min: 80,
      max: 100,
      axisLine: { show: false },
      splitLine: { lineStyle: { color: 'rgba(0,212,255,0.1)' } },
      axisLabel: { color: '#aaddff', fontSize: 10 },
      name: '评分',
      nameTextStyle: { color: '#aaddff' },
    },
    series: [
      {
        name: '安全评分',
        type: 'bar',
        data: safetyData.map((item) => item.value),
        itemStyle: {
          color: (params) => {
            const colors = ['#00ff88', '#00d4ff', '#ffae00']
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: colors[params.dataIndex] },
              { offset: 1, color: colors[params.dataIndex] + '40' },
            ])
          },
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 212, 255, 0.5)',
          },
        },
      },
    ],
  }
}

/* =================== 基地物流运输图表 =================== */
const logisticsState = reactive({ option: {} })

const buildLogisticsOption = () => {
  // 使用数据服务获取物流运输数据
  const logisticsData = dataService.getLogisticsData()

  logisticsState.option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const dataIndex = params[0].dataIndex
        const data = logisticsData[dataIndex]
        return `${data.name}<br/>
                入库: ${data.inbound}车次<br/>
                出库: ${data.outbound}车次<br/>
                运输车辆: ${data.vehicles}辆`
      },
    },
    legend: {
      data: ['入库车次', '出库车次'],
      textStyle: { color: '#aaddff', fontSize: 10 },
      top: 5,
    },
    grid: { left: 30, right: 10, top: 35, bottom: 20 },
    xAxis: {
      type: 'category',
      data: logisticsData.map((item) => item.name),
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: {
        color: '#aaddff',
        fontSize: 10,
        interval: 0,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      splitLine: { lineStyle: { color: 'rgba(0,212,255,0.1)' } },
      axisLabel: { color: '#aaddff', fontSize: 10 },
      name: '车次',
      nameTextStyle: { color: '#aaddff' },
    },
    series: [
      {
        name: '入库车次',
        type: 'bar',
        data: logisticsData.map((item) => item.inbound),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00d4ff' },
            { offset: 1, color: 'rgba(0, 212, 255, 0.3)' },
          ]),
        },
      },
      {
        name: '出库车次',
        type: 'bar',
        data: logisticsData.map((item) => item.outbound),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00ff88' },
            { offset: 1, color: 'rgba(0, 255, 136, 0.3)' },
          ]),
        },
      },
    ],
  }
}

onMounted(() => {
  buildPersonnelOption()
  buildSafetyOption()
  buildLogisticsOption()
})
</script>

<style lang="less" scoped>
.bottom-content {
  display: flex;
  gap: 12px;
  height: 100%;
  min-height: 240px; // 确保最小高度

  .bottom-left {
    flex: 2;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 100%;
  }

  .bottom-center {
    flex: 1.5;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 100%;
  }

  .bottom-right {
    flex: 1.5;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 100%;
  }

  .data-title {
    display: flex;
    align-items: center;
    height: 36px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.15),
      rgba(0, 212, 255, 0.05)
    );
    backdrop-filter: blur(10px);

    .icon {
      font-size: 45px;
      color: #00d4ff;
      filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
    }

    .title {
      color: #75d1f0;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: 0.5px;
      margin-left: -8px;
    }
  }

  /* 通用内容卡片样式 */
  .content-card {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.06),
      rgba(0, 212, 255, 0.03)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 14px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 14px rgba(0, 212, 255, 0.08);
    flex: 1;
  }

  /* 人员配置卡片样式 */
  .personnel-card {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .personnel-chart-wrapper {
    flex: 1;
    height: 180px; // 设置固定高度确保图表显示
    min-height: 160px;

    /* 让内部 echartsInit 也填满 */
    > * {
      width: 100% !important;
      height: 100% !important;
    }
  }

  /* 安全指标卡片样式 */
  .safety-card {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .safety-chart-wrapper {
    flex: 1;
    height: 180px; // 设置固定高度确保图表显示
    min-height: 160px;

    /* 让内部 echartsInit 也填满 */
    > * {
      width: 100% !important;
      height: 100% !important;
    }
  }

  /* 物流运输卡片样式 */
  .logistics-card {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .logistics-chart-wrapper {
    flex: 1;
    height: 180px; // 设置固定高度确保图表显示
    min-height: 160px;

    /* 让内部 echartsInit 也填满 */
    > * {
      width: 100% !important;
      height: 100% !important;
    }
  }
}
</style>
