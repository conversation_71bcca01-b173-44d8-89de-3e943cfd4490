<template>
  <div ref="sceneContainer" class="scene-3d-background">
    <!-- 启停控制按钮 -->
    <div class="start-stop-control">
      <button
        @click="toggleAnimation"
        class="start-stop-button"
        :class="{
          'stop-state': isAnimationRunning,
          'start-state': !isAnimationRunning,
        }"
      >
        {{ isAnimationRunning ? '停止' : '启动' }}
      </button>
    </div>

    <!-- 线框模式切换按钮 -->
    <div class="wireframe-control">
      <button @click="toggleWireframe" class="wireframe-button">
        {{ isWireframe ? '实体模式' : '线框模式' }}
      </button>
    </div>

    <!-- 点击信息面板 -->
    <div
      v-if="clickInfo.visible"
      class="click-info-panel"
      :style="{
        left: clickInfo.position.x + 'px',
        top: clickInfo.position.y + 'px',
      }"
    >
      <div class="info-header">
        <div class="info-title">{{ clickInfo.objectName }}</div>
        <div class="info-type">{{ clickInfo.objectType }}</div>
      </div>
      <div class="info-content">
        <div
          class="info-item"
          v-for="(value, key) in clickInfo.details"
          :key="key"
        >
          <span class="info-label">{{ getInfoLabel(key) }}:</span>
          <span class="info-value">{{ formatInfoValue(key, value) }}</span>
        </div>
      </div>
      <div class="info-close" @click="clickInfo.visible = false">×</div>
    </div>

    <!-- 加载状态覆盖层 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载车间3D模型...</div>
        <div class="loading-progress">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: loadingProgress + '%' }"
            ></div>
          </div>
          <div class="progress-text">{{ loadingProgress }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import * as THREE from 'three'
// 导入Three.js提供的GLTF模型加载器
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { texture } from 'three/tsl'

const sceneContainer = ref(null)
let scene, camera, renderer, animationId, controls
let model = null
let rollerObjects = [] // 存储滚轴对象的数组（包括Mesh和Group）
let gearMeshes = [] // 存储齿轮Mesh的数组

// 线框模式状态
const isWireframe = ref(false)
// 存储原始材质的映射，用于恢复
const originalMaterials = new Map()

// 动画控制状态
const isAnimationRunning = ref(true)

// 射线投射器和鼠标位置
let raycaster = new THREE.Raycaster()
let mouse = new THREE.Vector2()
let selectedObject = null // 当前选中的对象

// 拖拽检测相关变量
let mouseDownPosition = { x: 0, y: 0 }
let isDragging = false
const dragThreshold = 5 // 拖拽阈值（像素）

// 点击事件相关状态
const clickInfo = ref({
  visible: false,
  objectName: '',
  objectType: '',
  position: { x: 0, y: 0 },
  details: {},
})

// 加载状态
const isLoading = ref(true)
const loadingProgress = ref(0)

// 切换动画启停
const toggleAnimation = () => {
  isAnimationRunning.value = !isAnimationRunning.value
  console.log('动画状态:', isAnimationRunning.value ? '启动' : '停止')
}

// 切换线框模式
const toggleWireframe = () => {
  isWireframe.value = !isWireframe.value

  if (model) {
    // 定义不进入线框模式的对象名称
    // 齿轮: Mesh类型
    // 滚轴: Group类型（包含子Mesh如cable893、cable893_1d等）
    // 铜箔: Mesh类型
    // 地面: Mesh类型
    const excludeFromWireframe = ['齿轮', '滚轴', '铜箔', '地面', '显示器']

    // 新的实现方式：递归检查每个对象的祖先路径
    const isExcludedObject = (object) => {
      // 检查对象本身的名称
      if (
        object.name &&
        excludeFromWireframe.some((excludeName) =>
          object.name.includes(excludeName),
        )
      ) {
        return true
      }

      // 递归检查父对象的名称
      let parent = object.parent
      while (parent && parent !== scene) {
        if (
          parent.name &&
          excludeFromWireframe.some((excludeName) =>
            parent.name.includes(excludeName),
          )
        ) {
          return true
        }
        parent = parent.parent
      }

      return false
    }

    model.traverse((child) => {
      // 只处理Mesh对象
      if (child.isMesh && child.material) {
        const shouldExclude = isExcludedObject(child)

        // 保存原始材质（如果还没有保存的话）
        if (!originalMaterials.has(child.uuid)) {
          originalMaterials.set(child.uuid, child.material)
        }

        if (shouldExclude) {
          // 排除的对象：恢复原始材质，确保不是线框模式
          const originalMaterial = originalMaterials.get(child.uuid)
          child.material = originalMaterial

          // 确保排除的对象不是线框模式
          if (Array.isArray(child.material)) {
            child.material.forEach((material) => {
              material.wireframe = false
              material.needsUpdate = true
            })
          } else {
            child.material.wireframe = false
            child.material.needsUpdate = true
          }
        } else {
          // 正常处理其他对象
          const applyWireframe = isWireframe.value

          if (Array.isArray(child.material)) {
            child.material.forEach((material) => {
              material.wireframe = applyWireframe

              // 线框模式下增加透明度
              if (applyWireframe) {
                material.transparent = true
                material.opacity = 0.1
              } else {
                material.transparent = false
                material.opacity = 1.0
              }

              material.needsUpdate = true
            })
          } else {
            child.material.wireframe = applyWireframe

            // 线框模式下增加透明度
            if (applyWireframe) {
              child.material.transparent = true
              child.material.opacity = 0.3
            } else {
              child.material.transparent = false
              child.material.opacity = 1.0
            }

            child.material.needsUpdate = true
          }
        }
      }
    })

    // 强制重新渲染
    if (renderer && scene && camera) {
      renderer.render(scene, camera)
    }
  }
}

// 信息面板辅助函数
const getInfoLabel = (key) => {
  const labels = {
    name: '名称',
    type: '类型',
    category: '分类',
    function: '功能',
    status: '状态',
    speed: '转速',
    resolution: '分辨率',
    thickness: '厚度',
    quality: '质量',
    cuttingLossRate: '切割损失率',
    efficiency: '工作效率',
    temperature: '工作温度',
  }
  return labels[key] || key
}

const formatInfoValue = (key, value) => {
  if (typeof value === 'object' && value !== null) {
    return JSON.stringify(value)
  }
  return value
}

// 计算3D对象在屏幕上的位置
const getObjectScreenPosition = (object) => {
  if (!object || !camera || !renderer) {
    return { x: 0, y: 0 }
  }

  // 获取对象的世界坐标
  const worldPosition = new THREE.Vector3()
  object.getWorldPosition(worldPosition)

  // 将世界坐标转换为屏幕坐标
  const screenPosition = worldPosition.clone()
  screenPosition.project(camera)

  // 转换为像素坐标
  const canvas = renderer.domElement
  const x = (screenPosition.x * 0.5 + 0.5) * canvas.clientWidth
  const y = (screenPosition.y * -0.5 + 0.5) * canvas.clientHeight

  // 调整位置，让信息面板显示在对象上方
  return {
    x: x,
    y: y - 50, // 向上偏移50像素，显示在对象上方
  }
}

// 获取对象详细信息
const getObjectDetails = (object) => {
  const details = {
    name: object.name || '未命名对象',
    type: object.type,
  }

  // 根据对象名称添加特定信息
  if (object.name.includes('滚轴')) {
    details.category = '传动系统'
    details.function = '铜箔传输滚轴'
    details.status = '运行中'
    details.speed = '120 RPM'
  } else if (object.name.includes('齿轮')) {
    details.category = '传动系统'
    details.function = '动力传输齿轮'
    details.status = '运行中'
    details.speed = '80 RPM'
  } else if (object.name.includes('收集机')) {
    details.category = '收集系统'
    details.function = '铜箔收集设备'
    details.status = '运行中'
    details.speed = '95 RPM'
    details.cuttingLossRate = '2.3%'
    details.efficiency = '97.7%'
    details.temperature = '45°C'
  } else if (object.name.includes('显示器')) {
    details.category = '监控系统'
    details.function = '生产监控显示器'
    details.status = '正常显示'
    details.resolution = '1920x1080'
  } else if (object.name.includes('铜箔')) {
    details.category = '产品'
    details.function = '锂电铜箔产品'
    details.thickness = '8μm'
    details.quality = '优良'
  } else {
    details.category = '设备组件'
    details.function = '生产设备部件'
    details.status = '正常'
  }

  return details
}

// 鼠标按下事件处理
const onMouseDown = (event) => {
  mouseDownPosition.x = event.clientX
  mouseDownPosition.y = event.clientY
  isDragging = false
}

// 鼠标松开事件处理
const onMouseUp = (event) => {
  // 计算鼠标移动距离
  const deltaX = Math.abs(event.clientX - mouseDownPosition.x)
  const deltaY = Math.abs(event.clientY - mouseDownPosition.y)
  const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

  // 如果移动距离小于阈值，认为是点击而不是拖拽
  if (distance < dragThreshold) {
    if (event.button === 0) {
      // 左键点击
      onMouseClick(event)
    } else if (event.button === 2) {
      // 右键点击 - 取消选中
      onRightClick(event)
    }
  }

  isDragging = false
}

// 鼠标右键点击事件处理
const onRightClick = (event) => {
  // 阻止默认的右键菜单
  event.preventDefault()

  // 取消当前选中的对象
  if (selectedObject) {
    highlightObject(selectedObject, false)
    selectedObject = null
    clickInfo.value.visible = false
    console.log('右键取消选中')
  }
}

// 鼠标点击事件处理
const onMouseClick = (event) => {
  if (!camera || !scene) return

  // 计算鼠标在标准化设备坐标中的位置
  const rect = renderer.domElement.getBoundingClientRect()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

  // 更新射线投射器
  raycaster.setFromCamera(mouse, camera)

  // 计算与场景中对象的交点
  const intersects = raycaster.intersectObjects(scene.children, true)

  if (intersects.length > 0) {
    const clickedObject = intersects[0].object

    // 如果点击的是已选中的对象，则取消选中
    if (selectedObject === clickedObject) {
      highlightObject(selectedObject, false)
      selectedObject = null
      clickInfo.value.visible = false
      console.log('取消选中对象:', clickedObject.name)
    } else {
      // 如果之前有选中的对象，先取消高亮
      if (selectedObject) {
        highlightObject(selectedObject, false)
      }

      // 选中新对象
      selectedObject = clickedObject
      highlightObject(clickedObject, true)

      // 获取对象详细信息
      const details = getObjectDetails(clickedObject)

      // 计算3D对象在屏幕上的位置
      const screenPosition = getObjectScreenPosition(clickedObject)

      // 更新点击信息
      clickInfo.value = {
        visible: true,
        objectName: details.name,
        objectType: details.category,
        position: {
          x: screenPosition.x,
          y: screenPosition.y,
        },
        details: details,
      }

      console.log('选中对象:', details)
    }
  } else {
    // 点击空白区域，取消选中并隐藏信息面板
    if (selectedObject) {
      highlightObject(selectedObject, false)
      selectedObject = null
    }
    clickInfo.value.visible = false
    console.log('取消选中')
  }
}

// 高亮对象
const highlightObject = (object, highlight) => {
  if (!object || !object.material) return

  if (highlight) {
    // 保存原始材质
    if (!originalMaterials.has(object.uuid + '_highlight')) {
      originalMaterials.set(object.uuid + '_highlight', object.material.clone())
    }

    // 应用高亮效果
    if (Array.isArray(object.material)) {
      object.material.forEach((material) => {
        material.emissive.setHex(0x00d4ff) // 选中时的蓝色发光
        material.needsUpdate = true
      })
    } else {
      object.material.emissive.setHex(0x00d4ff)
      object.material.needsUpdate = true
    }
  } else {
    // 恢复原始材质
    if (Array.isArray(object.material)) {
      object.material.forEach((material) => {
        material.emissive.setHex(0x000000)
        material.needsUpdate = true
      })
    } else {
      object.material.emissive.setHex(0x000000)
      object.material.needsUpdate = true
    }
  }
}

// 初始化3D场景
const initScene = () => {
  // 创建场景
  scene = new THREE.Scene()

  // 获取容器尺寸
  const containerWidth = sceneContainer.value?.clientWidth || 800
  const containerHeight = sceneContainer.value?.clientHeight || 600

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75, // 视野角度
    containerWidth / containerHeight, // 宽高比
    1, // 近裁剪面 - 增大near值提高精度比例
    10000, // 远裁剪面 - 大幅提高可见范围
  )
  camera.position.set(0, 0, 5)

  // 创建渲染器 - 性能优化版本
  renderer = new THREE.WebGLRenderer({
    antialias: true, // 开启抗锯齿
    alpha: true,
    powerPreference: 'high-performance', // 使用高性能GPU
  })
  renderer.setSize(containerWidth, containerHeight)
  renderer.setClearColor(0x000000, 0) // 透明背景
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5)) // 限制像素比，提升性能
  renderer.shadowMap.enabled = false // 关闭阴影，大幅提升性能
  renderer.sortObjects = false // 禁用自动排序
  renderer.toneMapping = THREE.LinearToneMapping // 使用更简单的色调映射
  renderer.toneMappingExposure = 1.0 // 降低曝光度
  renderer.outputColorSpace = THREE.SRGBColorSpace

  // 将渲染器添加到DOM
  if (sceneContainer.value) {
    sceneContainer.value.appendChild(renderer.domElement)
  }

  // 添加鼠标事件监听器
  renderer.domElement.addEventListener('mousedown', onMouseDown)
  renderer.domElement.addEventListener('mouseup', onMouseUp)
  // 禁用右键菜单
  renderer.domElement.addEventListener('contextmenu', (e) => e.preventDefault())

  // 创建轨道控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.screenSpacePanning = false // 禁用屏幕空间平移
  controls.minDistance = 1.5 // 最小缩放距离 - 匹配near值，防止裁剪
  controls.maxDistance = 8000 // 最大缩放距离 - 充分利用远裁剪面范围

  // 限制垂直旋转角度（上下视角限制）
  controls.minPolarAngle = Math.PI / 6 // 最小角度：30度（限制向上看的角度）
  controls.maxPolarAngle = (Math.PI * 5) / 10 // 最大角度：150度（限制向下看的角度）

  // 可选：限制水平旋转角度（左右视角限制）
  // controls.minAzimuthAngle = -Math.PI / 2 // 最小水平角度：-90度
  // controls.maxAzimuthAngle = Math.PI / 2 // 最大水平角度：90度

  // 添加光源
  addLights()

  // 加载模型
  loadModel()
}

// 添加光源 - 性能优化版本，只保留必要光源
const addLights = () => {
  // 环境光 - 提供基础照明
  const ambientLight = new THREE.AmbientLight(0x404040, 1.2)
  scene.add(ambientLight)

  // 主方向光 - 提供主要照明和立体感
  const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.3)
  directionalLight1.position.set(500, 500, 300)
  scene.add(directionalLight1)

  // 第二个方向光 - 从左侧照明
  const directionalLight2 = new THREE.DirectionalLight(0xffffff, 1.0)
  directionalLight2.position.set(-400, 300, 200)
  scene.add(directionalLight2)

  // 第三个方向光 - 从后方照明
  const directionalLight3 = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight3.position.set(100, 200, -450)
  scene.add(directionalLight3)

  // 第四个方向光 - 从底部照明
  const directionalLight4 = new THREE.DirectionalLight(0xffffff, 0.6)
  directionalLight4.position.set(-150, -350, 250)
  scene.add(directionalLight4)

  // 添加聚光灯 - 有明确的光照范围
  const spotLight1 = new THREE.SpotLight(
    0xffffff,
    1.8,
    800,
    Math.PI / 4,
    0.2,
    1,
  )
  spotLight1.position.set(0, 600, 0)
  spotLight1.target.position.set(0, 0, 0)
  scene.add(spotLight1)
  scene.add(spotLight1.target)

  // 第二个聚光灯 - 从侧面照射
  const spotLight2 = new THREE.SpotLight(
    0xffffff,
    1.2,
    350,
    Math.PI / 8,
    0.15,
    1,
  )
  spotLight2.position.set(200, 150, 200)
  spotLight2.target.position.set(0, 0, 0)
  scene.add(spotLight2)
  scene.add(spotLight2.target)
}

// 加载GLB模型
const loadModel = () => {
  const loader = new GLTFLoader()
  isLoading.value = true
  loadingProgress.value = 0
  loader.load(
    '/model/未命名18.glb', // 模型文件路径
    (gltf) => {
      model = gltf.scene
      model.traverse((child) => {
        if (child.name && child.name.includes('滚轴'))
          rollerObjects.push(child)
      })
      model.traverse((child) => {
        if (child.isMesh && child.name && child.name.includes('齿轮')) 
          gearMeshes.push(child)
      })
      model.traverse((child) => {
        if (child.isMesh && child.name && child.name.includes('显示器1屏幕')) {
          const textureLoader = new THREE.TextureLoader()
          textureLoader.load(
            '/image.png', // 纹理图片路径
            (texture) => {
              const screenMaterial = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                opacity: 1.0,
              })
              child.material = screenMaterial
            },
            undefined,
            (error) => {
              console.error('图片加载失败:', error)
            },
          )
        }
      })
      const box = new THREE.Box3().setFromObject(model)
      const center = box.getCenter(new THREE.Vector3())
      const size = box.getSize(new THREE.Vector3())
      model.position.sub(center)
      const maxDim = Math.max(size.x, size.y, size.z) // 最大边长
      const fov = camera.fov * (Math.PI / 180)        // 视角转弧度
      let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2)) // 三角计算Z轴距离
      cameraZ *= 0.4 // 再远一点，避免太近
      camera.position.set(0, 0, cameraZ)
      camera.lookAt(0, 0, 0)
      scene.add(model)
      loadingProgress.value = 100
      setTimeout(() => {
        isLoading.value = false
      }, 300)
    },
    (progress) => {
      if (progress.lengthComputable) {
        const percentComplete = (progress.loaded / progress.total) * 100
        loadingProgress.value = Math.round(percentComplete)
      }
    },
    (error) => {
      console.error('车间3D模型加载失败:', error)
      isLoading.value = false
    },
  )
}

// 动态调整相机near值以优化深度精度
const updateCameraNear = () => {
  if (!controls || !camera) return

  // 根据当前相机距离动态调整near值
  const distance = controls.getDistance()
  const optimalNear = Math.max(distance * 0.001, 0.1) // near值为距离的0.1%，最小0.1

  if (Math.abs(camera.near - optimalNear) > 0.01) {
    camera.near = optimalNear
    camera.updateProjectionMatrix()
  }
}

// 更新信息面板位置
const updateInfoPanelPosition = () => {
  if (clickInfo.value.visible && selectedObject) {
    const screenPosition = getObjectScreenPosition(selectedObject)
    clickInfo.value.position.x = screenPosition.x
    clickInfo.value.position.y = screenPosition.y
  }
}

// 帧率限制变量
let lastTime = 0
const targetFPS = 30 // 限制到30FPS，降低GPU负担
const frameInterval = 1000 / targetFPS

// 动画循环 - 性能优化版本
const animate = (currentTime) => {
  animationId = requestAnimationFrame(animate)

  // 帧率限制
  if (currentTime - lastTime < frameInterval) {
    return
  }
  lastTime = currentTime

  // 更新控制器
  if (controls) {
    controls.update()
  }

  // 只有在动画运行状态下才执行旋转动画
  if (isAnimationRunning.value) {
    // 旋转所有滚轴对象（Mesh和Group）
    rollerObjects.forEach((obj) => {
      // 绕X轴旋转，模拟滚轴滚动效果
      obj.rotation.x -= 0.08
    })

    // 旋转所有齿轮Mesh
    gearMeshes.forEach((gear) => {
      // 绕Z轴旋转，模拟齿轮转动效果
      gear.rotation.x -= 0.05
    })
  }

  // 更新信息面板位置
  updateInfoPanelPosition()

  // 动态优化深度精度
  updateCameraNear()

  renderer.render(scene, camera)
}

// 处理窗口大小变化
const handleResize = () => {
  if (!camera || !renderer || !sceneContainer.value) return

  const containerWidth = sceneContainer.value.clientWidth
  const containerHeight = sceneContainer.value.clientHeight

  camera.aspect = containerWidth / containerHeight
  camera.updateProjectionMatrix()
  renderer.setSize(containerWidth, containerHeight)
}

// 清理资源
const cleanup = () => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }

  if (controls) {
    controls.dispose()
  }

  if (renderer) {
    // 移除事件监听器
    renderer.domElement.removeEventListener('mousedown', onMouseDown)
    renderer.domElement.removeEventListener('mouseup', onMouseUp)
    renderer.domElement.removeEventListener('contextmenu', (e) =>
      e.preventDefault(),
    )
    renderer.dispose()
  }

  // 清理几何体和材质
  scene?.traverse((object) => {
    if (object.geometry) {
      object.geometry.dispose()
    }
    if (object.material) {
      if (Array.isArray(object.material)) {
        object.material.forEach((material) => material.dispose())
      } else {
        object.material.dispose()
      }
    }
  })

  window.removeEventListener('resize', handleResize)

  // 清空滚轴数组
  rollerObjects = []
  // 清空齿轮数组
  gearMeshes = []
  // 清空选中对象
  selectedObject = null
  // 清空原始材质映射
  originalMaterials.clear()
}

onMounted(() => {
  initScene()
  animate()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  cleanup()
})
</script>

<style lang="less" scoped>
.scene-3d-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;

  canvas {
    display: block;
    width: 100% !important;
    height: 100% !important;
  }
}

.start-stop-control {
  position: absolute;
  bottom: 20px;
  right: 140px;
  z-index: 1000;
  pointer-events: auto;
}

.wireframe-control {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  pointer-events: auto;
}

.start-stop-button {
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.start-state {
    background: #28a745;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);

    &:hover {
      background: #218838;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
    }
  }

  &.stop-state {
    background: #dc3545;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);

    &:hover {
      background: #c82333;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
    }
  }
}

.wireframe-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);

  &:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
  }
}

/* 点击信息面板样式 */
.click-info-panel {
  position: fixed;
  background: linear-gradient(
    145deg,
    rgba(0, 212, 255, 0.95),
    rgba(0, 153, 204, 0.95)
  );
  border: 1px solid rgba(0, 212, 255, 0.8);
  border-radius: 12px;
  padding: 16px;
  min-width: 280px;
  max-width: 400px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.3);
  z-index: 2000;
  pointer-events: auto;
  transform: translate(-50%, -100%);
  margin-top: -10px;
  animation: fadeInUp 0.3s ease-out;

  &::before {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid rgba(0, 212, 255, 0.95);
  }

  .info-header {
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 8px;

    .info-title {
      color: white;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .info-type {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
      font-weight: 500;
    }
  }

  .info-content {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
      font-size: 13px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        min-width: 60px;
      }

      .info-value {
        color: white;
        font-weight: 600;
        text-align: right;
        flex: 1;
        margin-left: 8px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .info-close {
    position: absolute;
    top: 8px;
    right: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      transform: scale(1.1);
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, -100%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -100%) translateY(0);
  }
}

/* 加载状态覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.loading-content {
  text-align: center;
  color: white;
  max-width: 300px;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(0, 212, 255, 0.3);
  border-top: 4px solid #00d4ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 20px;
  color: #00d4ff;
}

.loading-progress {
  margin-top: 15px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff, #0099cc);
  border-radius: 3px;
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.progress-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
