<template>
  <div class="right-content">
    <!-- 车间综合评估雷达图 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:radar icon"></i>
        </decoFrameB2>
        <span class="title">车间综合评估</span>
      </div>
      <div class="data-card-body">
        <div class="workshop-radar-container">
          <echartsInit :chartOption="workshopRadarState.chartOption" />
        </div>
      </div>
    </div>

    <!-- 生产总览 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:build-tool icon"></i>
        </decoFrameB2>
        <span class="title">排产执行总览</span>
      </div>
      <div class="data-card-body">
        <div class="screenA-counterGrid">
          <aYinTechBorderB3
            :config="borderConfig(index)"
            v-for="(item, index) in state1.arry"
            :key="index"
          >
            <div class="inner-content">
              <div class="block-title">
                {{ item.title }} <span v-if="item.unit">({{ item.unit }})</span>
              </div>
              <div class="total">
                <i :class="[item.icon, 'icon']"></i>
                <DigitalTransform
                  class="numbers"
                  :value="item.total"
                  :useGrouping="true"
                  :interval="1000"
                />
              </div>
            </div>
          </aYinTechBorderB3>
        </div>
        <!-- 本周生产量 -->
        <div class="production-chart-card">
          <div class="chart-header">
            <i class="i carbon:chart-column chart-icon"></i>
            <span class="chart-title">本周铜箔排产完成情况</span>
          </div>
          <div class="chart-container">
            <echartsInit :chartOption="productionState.chartOption" />
          </div>
        </div>
      </div>
    </div>

    <!-- 介质用量统计 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:flow-stream icon"></i>
        </decoFrameB2>
        <span class="title">铜箔生产原料消耗</span>
      </div>
      <div class="data-card-body">
        <!-- 介质用量数据展示 -->
        <div class="medium-usage-grid">
          <div
            class="usage-card"
            v-for="(item, index) in mediumState.usageData"
            :key="index"
          >
            <div class="card-content">
              <div class="left-section">
                <i :class="['i', item.icon, 'medium-icon']"></i>
                <span class="medium-name">{{ item.name }}</span>
              </div>
              <div class="right-section">
                <DigitalTransform
                  class="usage-value"
                  :useGrouping="true"
                  :value="item.value"
                  :interval="1500"
                />
                <span class="usage-unit">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import * as echarts from 'echarts'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

// decoFrameB2 配置
const decoFrameConfig = {
  directionAlt: true,
  scale: 0.8,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 40,
}

// 铜箔生产原料消耗数据状态 - 使用数据服务
const mediumState = reactive({
  usageData: dataService.getMaterialsData(),
})

// 本周铜箔排产完成情况数据状态 - 使用数据服务
const productionState = reactive({
  chartData: dataService.getWeeklyProductionData(),
  chartOption: {},
})

// 车间综合评估雷达图数据 - 使用数据服务
const workshopRadarState = reactive({
  chartOption: {},
  radarData: dataService.getWorkshopRadarData(),
})

// 排产执行总览数据 - 使用数据服务
const state1 = reactive(dataService.getSchedulingOverviewData())

const borderConfig = (index) => {
  let rotate = null
  if (index === 0) rotate = 'x'
  else if (index === 1) rotate = 'all'
  else if (index === 3) rotate = 'y'
  return { dur: 3, opacity: 0.7, rotate }
}

// 配置排产完成情况对比图
const processProductionOption = () => {
  productionState.chartOption = {
    update: false,
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = params[0].name + '<br/>'
        params.forEach((param) => {
          result +=
            param.marker + param.seriesName + ': ' + param.value + '吨<br/>'
        })
        if (params.length >= 2) {
          const completion = (
            (params[1].value / params[0].value) *
            100
          ).toFixed(1)
          result += '完成率: ' + completion + '%'
        }
        return result
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff', fontSize: 12 },
    },
    legend: {
      data: ['计划产量', '实际产量'],
      textStyle: { color: '#00d4ff', fontSize: 11 },
      top: '5%',
      right: '5%',
    },
    grid: {
      left: '0%',
      right: '6%',
      top: '20%',
      bottom: '0%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: productionState.chartData.categories,
      axisLine: { lineStyle: { color: '#00d4ff', width: 1 } },
      axisLabel: { color: '#00d4ff', fontSize: 11 },
      axisTick: { show: false },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#66ccff', fontSize: 11, formatter: '{value}吨' },
      splitLine: {
        lineStyle: { color: 'rgba(0, 212, 255, 0.1)', type: 'dashed' },
      },
    },
    series: [
      {
        name: '计划产量',
        type: 'bar',
        data: productionState.chartData.planned,
        barWidth: '35%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 174, 0, 0.8)' },
              { offset: 1, color: 'rgba(255, 174, 0, 0.4)' },
            ],
          },
          borderRadius: [4, 4, 0, 0],
        },
      },
      {
        name: '实际产量',
        type: 'bar',
        data: productionState.chartData.actual,
        barWidth: '35%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#00d4ff' },
              { offset: 1, color: '#0099cc' },
            ],
          },
          borderRadius: [4, 4, 0, 0],
        },
        emphasis: {
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#33ddff' },
                { offset: 1, color: '#00aadd' },
              ],
            },
          },
        },
      },
    ],
  }
}

// 配置车间综合评估雷达图
const processWorkshopRadarOption = () => {
  workshopRadarState.chartOption = {
    update: false,
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const data = params.data
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; color: #00d4ff; margin-bottom: 6px;">${
              data.name
            }</div>
            <div style="color: #ffffff; font-size: 12px; line-height: 1.5;">
              ${workshopRadarState.radarData.indicators
                .map(
                  (indicator, index) =>
                    `<div>${indicator.name}：${data.value[index]}%</div>`,
                )
                .join('')}
            </div>
          </div>
        `
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff' },
    },
    legend: {
      show: false,
    },
    radar: {
      indicator: workshopRadarState.radarData.indicators,
      center: ['50%', '50%'],
      radius: '70%',
      startAngle: 90,
      splitNumber: 4,
      shape: 'polygon',
      name: {
        formatter: '{value}',
        textStyle: {
          color: '#00d4ff',
          fontSize: 11,
          fontWeight: 'bold',
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 212, 255, 0.3)',
          width: 1,
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: [
            'rgba(0, 212, 255, 0.05)',
            'rgba(0, 212, 255, 0.1)',
            'rgba(0, 212, 255, 0.05)',
            'rgba(0, 212, 255, 0.1)',
          ],
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 212, 255, 0.5)',
          width: 1,
        },
      },
    },
    series: [
      {
        name: '车间评估',
        type: 'radar',
        data: workshopRadarState.radarData.values.map((item) => ({
          ...item,
          lineStyle: {
            color: item.itemStyle.color,
            width: 2,
          },
          areaStyle: {
            color: item.itemStyle.color,
            opacity: 0.2,
          },
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: item.itemStyle.color,
            borderColor: '#ffffff',
            borderWidth: 1,
          },
        })),
        emphasis: {
          lineStyle: { width: 3 },
          areaStyle: { opacity: 0.4 },
        },
      },
    ],
  }
}

onMounted(() => {
  processProductionOption()
  processWorkshopRadarOption()
})
</script>

<style lang="less" scoped>
.right-content {
  padding: 10px;

  .data-header {
    display: flex;
    align-items: center;
    height: 42px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.15),
      rgba(0, 212, 255, 0.05)
    );
    backdrop-filter: blur(10px);

    .icon {
      font-size: 45px;
      color: #00d4ff;
      filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
    }

    .title {
      color: #75d1f0;
      font-size: 26px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .data-card-body {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: auto;
    padding: 10px;
    position: relative;
  }

  // 介质用量统计样式
  .medium-usage-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    box-sizing: border-box;
    width: 100%;
  }

  .usage-card {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.15);
    border-radius: 8px;
    backdrop-filter: blur(6px);
    box-shadow: 0 2px 8px rgba(0, 212, 255, 0.06);
    transition: all 0.2s ease;
    min-height: 60px;
    box-sizing: border-box;
    overflow: hidden;

    .card-content {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      padding: 0 12px;
      width: 100%;

      .left-section {
        display: flex;
        align-items: center;
        gap: 6px;
        flex: 0 0 auto;
        max-width: 60%;

        .medium-icon {
          font-size: 24px;
          color: #00d4ff;
          filter: drop-shadow(0 0 6px rgba(0, 212, 255, 0.4));
          flex-shrink: 0;
        }

        .medium-name {
          color: #00d4ff;
          font-size: 16px;
          font-weight: 700;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .right-section {
        display: flex;
        align-items: baseline;
        gap: 4px;
        flex: 0 0 auto;
        justify-content: flex-end;

        .usage-value {
          color: #00d4ff;
          font-size: 22px;
          font-weight: 800;
          line-height: 1;
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .usage-unit {
          color: rgba(0, 212, 255, 0.8);
          font-size: 14px;
          font-weight: 700;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }

  // 生产量柱状图样式
  .production-chart-card {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    padding: 12px;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.1);
    transition: all 0.3s ease;

    .chart-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(0, 212, 255, 0.15);

      .chart-icon {
        font-size: 18px;
        color: #00d4ff;
        filter: drop-shadow(0 0 6px rgba(0, 212, 255, 0.4));
      }

      .chart-title {
        color: #00d4ff;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 0.3px;
      }
    }

    .chart-container {
      height: 180px;
      position: relative;
    }
  }
  // 车间雷达图样式
  .workshop-radar-container {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    padding: 8px;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.1);
    transition: all 0.3s ease;
    height: 200px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(
        circle,
        rgba(0, 212, 255, 0.03) 0%,
        transparent 70%
      );
      animation: radarPulse 6s ease-in-out infinite;
      pointer-events: none;
    }
  }
}

@keyframes radarPulse {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05) rotate(2deg);
    opacity: 0.1;
  }
}

.screenA-counterGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  height: 200px;
  grid-template-rows: repeat(2, 1fr);
  position: relative;

  .aYinTechBorderB3 {
    padding: 0;
    position: relative;

    .inner-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 8px;

      .block-title {
        font-size: 14px;
        color: #00d4ff;
        margin-bottom: 8px;
        text-align: center;
        line-height: 1.2;
        font-weight: 600;
      }

      .total {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;

        i {
          font-size: 28px;
          color: #00d4ff;
          flex-shrink: 0;
          filter: drop-shadow(0 2px 4px rgba(0, 212, 255, 0.4));
        }

        .numbers {
          font-size: 28px;
          color: #00d4ff;
          font-weight: 800;
          line-height: 1;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
</style>
