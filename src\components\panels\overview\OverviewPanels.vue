<template>
  <div class="overview-panels">
    <OverviewRightContent v-if="panelType === 'right'" />
    <OverviewBottomContent v-if="panelType === 'bottom'" />
  </div>
</template>

<script setup>
import OverviewRightContent from './OverviewRightContent.vue'
import OverviewBottomContent from './OverviewBottomContent.vue'

const props = defineProps({
  panelType: {
    type: String,
    required: true,
    validator: (value) => ['right', 'bottom'].includes(value),
  },
})
</script>

<style lang="less" scoped>
.overview-panels {
  height: 100%;
}
</style>
