<template>
  <div class="right-content">
    <!-- 订单概览 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:shopping-cart icon"></i>
        </decoFrameB2>
        <span class="title">订单状态分布</span>
      </div>
      <div class="data-card-body">
        <!-- 关键指标卡片 -->
        <div class="overview-stats">
          <div class="stat-card" v-for="(item, idx) in stats" :key="idx">
            <i :class="['i', item.icon, 'stat-icon']"></i>
            <div class="stat-value">
              <DigitalTransform
                v-if="!item.unit"
                :value="item.value"
                :useGrouping="true"
              />
              <template v-else>
                <DigitalTransform :value="item.value" :useGrouping="true" />
                <span class="stat-unit">{{ item.unit }}</span>
              </template>
            </div>
            <div class="stat-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单生产进度监控 -->
    <div class="data-card production-progress-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:assembly-cluster icon"></i>
        </decoFrameB2>
        <span class="title">订单生产进度监控</span>
      </div>
      <div class="data-card-body">
        <div class="progress-list">
          <div
            class="progress-item"
            v-for="item in productionProgress"
            :key="item.orderId"
          >
            <div class="progress-header">
              <div class="order-info">
                <span class="order-id">{{ item.orderId }}</span>
                <span class="product-spec">{{ item.productSpec }}</span>
              </div>
              <div class="production-line">
                <i class="i carbon:machine-learning"></i>
                {{ item.productionLine }}
              </div>
            </div>
            <div class="progress-details">
              <div class="process-steps">
                <div
                  class="step"
                  v-for="(step, idx) in item.processSteps"
                  :key="idx"
                  :class="{
                    completed: step.status === 'completed',
                    active: step.status === 'active',
                    pending: step.status === 'pending',
                  }"
                >
                  <div class="step-icon">
                    <i :class="['i', step.icon]"></i>
                  </div>
                  <div class="step-name">{{ step.name }}</div>
                  <div class="step-progress" v-if="step.status === 'active'">
                    {{ step.progress }}%
                  </div>
                </div>
              </div>
              <div class="overall-progress">
                <div class="progress-info">
                  <span class="progress-text"
                    >总进度: {{ item.overallProgress }}%</span
                  >
                  <span class="eta"
                    >预计完成: {{ item.estimatedCompletion }}</span
                  >
                </div>
                <div class="progress-bar-container">
                  <div
                    class="progress-bar"
                    :style="{ width: item.overallProgress + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单时间轴 -->
    <div class="data-card timeline-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:time icon"></i>
        </decoFrameB2>
        <span class="title">最新订单</span>
      </div>
      <div class="data-card-body">
        <div class="order-timeline">
          <div class="timeline-track">
            <div class="timeline-item" v-for="item in orders" :key="item.id">
              <div class="t-date">{{ item.date }}</div>
              <div class="t-dot" :class="item.status"></div>
              <div class="t-info">
                <div class="o-id">{{ item.id }}</div>
                <div class="o-product">{{ item.product }}</div>
                <div class="o-status">{{ item.statusText }}</div>
                <div class="o-progress">
                  <div
                    class="progress-bar"
                    :style="{ width: item.progress + '%' }"
                  ></div>
                </div>
              </div>
            </div>
            <!-- 克隆一份实现无缝循环 -->
            <div
              class="timeline-item"
              v-for="item in orders"
              :key="item.id + '-dup'"
            >
              <div class="t-date">{{ item.date }}</div>
              <div class="t-dot" :class="item.status"></div>
              <div class="t-info">
                <div class="o-id">{{ item.id }}</div>
                <div class="o-product">{{ item.product }}</div>
                <div class="o-status">{{ item.statusText }}</div>
                <div class="o-progress">
                  <div
                    class="progress-bar"
                    :style="{ width: item.progress + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 订单列表 -->
  </div>
</template>

<script setup>
import { reactive, onMounted, ref, nextTick } from 'vue'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

// decoFrameB2 配置
const decoFrameConfig = {
  directionAlt: true,
  scale: 0.8,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 40,
}

// 概览统计数据 - 使用数据服务
const stats = reactive(dataService.getOrderManagementData().stats)

// 订单时间轴数据 - 使用数据服务
const orders = reactive(dataService.getOrderManagementData().orders)

// 订单生产进度监控数据 - 使用数据服务
const productionProgress = reactive(
  dataService.getOrderManagementData().productionProgress,
)

// 可选：自动滚动效果
const timelineRef = ref(null)
const startAutoScroll = () => {
  const el = timelineRef.value
  if (!el) return
  const scroll = () => {
    el.scrollTop += 1
    if (el.scrollTop >= el.scrollHeight - el.clientHeight) {
      el.scrollTop = 0
    }
  }
  setInterval(scroll, 100)
}

onMounted(() => {
  nextTick(startAutoScroll)
})
</script>

<style lang="less" scoped>
.right-content {
  padding: 10px;
  display: flex;
  flex-direction: column;

  .data-header {
    display: flex;
    align-items: center;
    height: 42px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.15),
      rgba(0, 212, 255, 0.05)
    );
    backdrop-filter: blur(10px);

    .icon {
      font-size: 45px;
      color: #00d4ff;
      filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
    }

    .title {
      color: #75d1f0;
      font-size: 26px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .data-card-body {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: auto;
    padding: 10px;
    position: relative;
  }

  .pie-chart-card {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;

    .power-consumption-card {
      display: flex;
      justify-content: space-between;
      background: linear-gradient(
        145deg,
        rgba(0, 212, 255, 0.12),
        rgba(0, 212, 255, 0.06)
      );
      border: 1px solid rgba(0, 212, 255, 0.25);
      border-radius: 12px;
      padding: 10px;
      backdrop-filter: blur(12px);
      box-shadow: 0 4px 12px rgba(0, 212, 255, 0.1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(0, 212, 255, 0.08) 0%,
          transparent 70%
        );
        animation: pulse 4s ease-in-out infinite;
        pointer-events: none;
      }

      .card-body-header {
        display: flex;
        align-items: center;
        position: relative;
        z-index: 1;

        .power-icon {
          font-size: 24px;
          color: #00d4ff;
          filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.6));
          animation: iconGlow 3s ease-in-out infinite alternate;
        }

        .card-title {
          color: #00d4ff;
          font-size: 18px;
          font-weight: 600;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          letter-spacing: 0.3px;
        }
      }

      .card-content {
        display: flex;
        align-items: baseline;
        gap: 6px;
        position: relative;
        z-index: 1;

        .power-value {
          color: #00d4ff;
          font-size: 28px;
          font-weight: 700;
          line-height: 1;
          display: inline-block;
        }

        .power-unit {
          color: rgba(0, 212, 255, 0.8);
          font-size: 16px;
          font-weight: 500;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          line-height: 1;
          display: inline-block;
          vertical-align: baseline;
        }
      }
    }

    .chart-container {
      height: 260px;
      position: relative;
      border-radius: 8px;
      overflow: visible;
    }
  }

  /* 概览统计样式 */
  .overview-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;

    .stat-card {
      background: rgba(0, 212, 255, 0.05);
      border: 1px solid rgba(0, 212, 255, 0.2);
      border-radius: 8px;
      padding: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;

      .stat-icon {
        font-size: 20px;
        color: #00d4ff;
      }

      .stat-value {
        font-size: 20px;
        font-weight: 700;
        color: #00d4ff;
        display: flex;
        align-items: baseline;

        .stat-unit {
          font-size: 14px;
          margin-left: 2px;
        }
      }

      .stat-label {
        font-size: 12px;
        color: #aaddff;
      }
    }
  }

  /* overview-radar 已删除 */

  /* 订单生产进度监控样式 */
  .production-progress-card {
    .progress-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      max-height: 450px;
      overflow-y: auto;

      /* 隐藏滚动条但保持功能 */
      scrollbar-width: none;
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .progress-item {
      background: rgba(0, 212, 255, 0.05);
      border: 1px solid rgba(0, 212, 255, 0.15);
      border-radius: 8px;
      padding: 12px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 212, 255, 0.08);
        border-color: rgba(0, 212, 255, 0.25);
      }
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .order-info {
        display: flex;
        flex-direction: column;
        gap: 2px;

        .order-id {
          color: #00d4ff;
          font-weight: 600;
          font-size: 14px;
        }

        .product-spec {
          color: #ffb74d;
          font-size: 12px;
        }
      }

      .production-line {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #aaddff;
        font-size: 12px;

        i {
          color: #00d4ff;
        }
      }
    }

    .process-steps {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      gap: 8px;

      .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        flex: 1;
        position: relative;

        .step-icon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          transition: all 0.3s ease;
        }

        .step-name {
          font-size: 10px;
          text-align: center;
          line-height: 1.2;
        }

        .step-progress {
          font-size: 10px;
          font-weight: 600;
          color: #00d4ff;
        }

        &.completed {
          .step-icon {
            background: #4caf50;
            color: white;
          }
          .step-name {
            color: #4caf50;
          }
        }

        &.active {
          .step-icon {
            background: #00d4ff;
            color: white;
            animation: pulse 2s infinite;
          }
          .step-name {
            color: #00d4ff;
          }
        }

        &.pending {
          .step-icon {
            background: rgba(255, 255, 255, 0.1);
            color: #666;
          }
          .step-name {
            color: #666;
          }
        }

        /* 连接线 */
        &:not(:last-child)::after {
          content: '';
          position: absolute;
          top: 12px;
          right: -4px;
          width: 8px;
          height: 2px;
          background: rgba(0, 212, 255, 0.3);
        }

        &.completed:not(:last-child)::after {
          background: #4caf50;
        }
      }
    }

    .overall-progress {
      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;

        .progress-text {
          color: #00d4ff;
          font-size: 12px;
          font-weight: 600;
        }

        .eta {
          color: #aaddff;
          font-size: 11px;
        }
      }

      .progress-bar-container {
        width: 100%;
        height: 6px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        overflow: hidden;

        .progress-bar {
          height: 100%;
          background: linear-gradient(90deg, #00d4ff, #0099cc);
          border-radius: 3px;
          transition: width 0.3s ease;
        }
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes iconGlow {
  0% {
    filter: drop-shadow(0 0 12px rgba(0, 212, 255, 0.6));
  }
  100% {
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.9));
  }
}

/* 订单时间轴 */
.order-timeline {
  flex: 1;
  max-height: 250px;
  overflow: hidden;
  position: relative;
  padding-left: 24px;
}

.timeline-track {
  animation: scroll-vert 15s linear infinite;
}

@keyframes scroll-vert {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

.order-timeline::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(0, 212, 255, 0.3);
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  position: relative;
  padding: 6px 8px;
  border: 1px solid rgba(0, 212, 255, 0.15);
  border-radius: 4px;
  background: rgba(0, 212, 255, 0.03);
}

.t-date {
  color: #66ccff;
  font-size: 12px;
  flex-shrink: 0;
  width: 80px;
}

.t-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  flex-shrink: 0;
}

.t-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
  color: #aaddff;
  flex: 1;

  .o-id {
    color: #00d4ff;
    font-weight: 600;
    margin-right: 6px;
  }

  .o-product {
    color: #ffb74d;
  }

  /* first row combine id + product */
  .first-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 2px;
  }

  .o-status {
    color: #aaddff;
  }

  .o-progress {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #00d4ff, #0099cc);
    }
  }
}

.t-dot.pending {
  background: #ffc107;
}

.t-dot.shipping {
  background: #00d4ff;
}

.t-dot.completed {
  background: #4caf50;
}

/* timeline card 占满剩余空间 */
.timeline-card {
  flex: 1;
  display: flex;
  flex-direction: column;

  .data-card-body {
    flex: 1;
    display: flex;
    padding: 10px;
  }
}
</style>
