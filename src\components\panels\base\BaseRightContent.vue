<template>
  <div class="base-right-content">
    <!-- 产品产量统计 -->
    <div class="data-card production-stats">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:chart-column icon"></i>
        </decoFrameB2>
        <span class="title">产品产量统计</span>
      </div>
      <div class="data-card-body">
        <div class="production-stats-wrapper">
          <echartsInit :chartOption="productionStatsState.option" />
        </div>
      </div>
    </div>

    <!-- 基地分布地图 -->
    <div class="data-card base-map">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:earth icon"></i>
        </decoFrameB2>
        <span class="title">基地分布</span>
      </div>
      <div class="data-card-body">
        <div class="base-map-container">
          <echartsInit :chartOption="mapState.chartOption" />
        </div>
      </div>
    </div>

    <!-- 实时产量监控 -->
    <div class="data-card production-monitor">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:analytics icon"></i>
        </decoFrameB2>
        <span class="title">实时产量监控</span>
      </div>
      <div class="data-card-body">
        <div class="production-chart-wrapper">
          <echartsInit :chartOption="productionMonitorState.option" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import chinaJson from '@/utils/map/中华人民共和国.json'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

// decoFrameB2 配置
const decoFrameConfig = {
  directionAlt: true,
  scale: 0.6,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 42,
}

/* =================== 产品产量统计 =================== */
const productionStatsState = reactive({ option: {} })

const buildProductionStatsOption = () => {
  // 使用数据服务获取当前基地的产品产量统计数据
  const productionStatsData = dataService.getCurrentBaseProductionStatsData()
  const products = productionStatsData.map((item) => item.title)
  const monthlyOutput = productionStatsData.map((item) => item.actual)
  const targetOutput = productionStatsData.map((item) => item.target)

  productionStatsState.option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params) => {
        const actual = params[0]
        const target = params[1]
        const completion = ((actual.value / target.value) * 100).toFixed(1)
        return `${actual.axisValue}<br/>实际产量: ${actual.value}吨<br/>目标产量: ${target.value}吨<br/>完成率: ${completion}%`
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      textStyle: { color: '#ffffff' },
    },
    legend: {
      data: ['实际产量', '目标产量'],
      textStyle: { color: '#aaddff', fontSize: 12 },
      top: 5,
    },
    grid: {
      left: 15,
      right: 15,
      top: 35,
      bottom: 25,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: products,
      axisLine: {
        lineStyle: { color: '#00d4ff' },
      },
      axisLabel: {
        color: '#aaddff',
        fontSize: 10,
        rotate: 15,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      name: '产量(吨)',
      nameTextStyle: { color: '#aaddff', fontSize: 11 },
      axisLine: { show: false },
      splitLine: {
        lineStyle: { color: 'rgba(0,212,255,0.1)' },
      },
      axisLabel: {
        color: '#aaddff',
        fontSize: 10,
      },
    },
    series: [
      {
        name: '实际产量',
        type: 'bar',
        data: monthlyOutput,
        barWidth: '35%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00d4ff' },
            { offset: 1, color: '#0099cc' },
          ]),
          borderRadius: [4, 4, 0, 0],
        },
        label: {
          show: true,
          position: 'top',
          color: '#00d4ff',
          fontSize: 10,
          formatter: '{c}',
        },
      },
      {
        name: '目标产量',
        type: 'bar',
        data: targetOutput,
        barWidth: '35%',
        itemStyle: {
          color: 'rgba(0, 212, 255, 0.3)',
          borderColor: '#00d4ff',
          borderWidth: 1,
          borderRadius: [4, 4, 0, 0],
        },
        label: {
          show: true,
          position: 'top',
          color: '#aaddff',
          fontSize: 10,
          formatter: '{c}',
        },
      },
    ],
  }
}

/* =================== 基地分布地图 =================== */
// 基地分布地图数据状态
const mapState = reactive({
  chartOption: {},
  // 使用数据服务获取当前基地数据
  ...dataService.getCurrentBaseMapData(),
  // 使用数据服务获取当前基地的订单飞线数据
  orderFlights: dataService.getCurrentBaseOrderFlights(),
})

// 配置基地分布地图
const processMapOption = () => {
  // 注册中国地图
  echarts.registerMap('china', chinaJson)

  // 获取当前基地坐标，自动聚焦
  const currentBase = mapState.baseData[0] // 当前基地
  const baseCoord = [currentBase.value[0], currentBase.value[1]] // [经度, 纬度]

  mapState.chartOption = {
    update: false,
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if (
          params.componentType === 'series' &&
          params.seriesType === 'scatter'
        ) {
          const data = params.data
          const baseInfo = mapState.baseData.find(
            (base) => base.name === data.name,
          )
          if (baseInfo) {
            return `
              <div style="padding: 8px;">
                <div style="font-weight: bold; color: #00d4ff; margin-bottom: 6px;">${
                  baseInfo.name
                }</div>
                <div style="color: #ffffff; font-size: 12px; line-height: 1.5;">
                  <div>产能：${baseInfo.capacity}</div>
                  <div>员工：${baseInfo.employees}人</div>
                  <div>状态：${
                    baseInfo.status === 'running' ? '正常运行' : '维护中'
                  }</div>
                  <div style="margin-top: 4px; color: #99ccff;">${
                    baseInfo.description
                  }</div>
                </div>
              </div>
            `
          }
        }
        return params.name
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff' },
    },
    geo: {
      map: 'china',
      roam: true, // 启用缩放和拖拽
      zoom: 3.5, // 提高缩放级别，更聚焦
      center: baseCoord, // 自动聚焦到当前基地位置
      scaleLimit: {
        min: 1.2, // 最小缩放比例
      },
      label: {
        show: false, // 隐藏省份名称
        color: 'rgba(0, 212, 255, 0.6)',
        fontSize: 10,
        fontWeight: 'normal',
      },
      itemStyle: {
        areaColor: 'rgba(0, 59, 92, 0.3)',
        borderColor: 'rgba(0, 212, 255, 0.4)',
        borderWidth: 1,
      },
      emphasis: {
        label: {
          show: false, // 悬浮时也不显示省份名称
          color: '#00d4ff',
          fontSize: 11,
        },
        itemStyle: {
          areaColor: 'rgba(0, 91, 139, 0.5)',
          borderColor: '#00d4ff',
          borderWidth: 2,
        },
      },
    },
    series: [
      {
        name: '基地分布',
        type: 'scatter',
        coordinateSystem: 'geo',
        symbol: 'pin',
        symbolSize: (val) => {
          // 根据产能大小调整图标大小
          const capacity = val[2]
          return Math.max(25, Math.min(45, capacity / 100 + 20))
        },
        data: mapState.baseData.map((base) => ({
          name: base.name,
          value: base.value,
          itemStyle: {
            color: base.status === 'running' ? '#00d4ff' : '#ffae00',
            shadowBlur: 8,
            shadowColor: base.status === 'running' ? '#00d4ff' : '#ffae00',
          },
        })),
        label: {
          show: true,
          position: 'bottom',
          color: '#00d4ff',
          fontSize: 11,
          fontWeight: 'bold',
          formatter: '{b}',
          offset: [0, 5],
        },
        emphasis: {
          scale: 1.2,
          label: {
            show: true,
            color: '#ffffff',
            fontSize: 12,
          },
        },
      },
      // 客户位置标记
      {
        name: '客户位置',
        type: 'scatter',
        coordinateSystem: 'geo',
        symbol: 'circle',
        symbolSize: 8,
        data: mapState.orderFlights.map((flight) => ({
          name: flight.toName,
          value: flight.toCoord,
          itemStyle: {
            color: flight.status === 'shipping' ? '#00ff88' : '#66ccff',
            shadowBlur: 4,
            shadowColor: flight.status === 'shipping' ? '#00ff88' : '#66ccff',
          },
        })),
        label: {
          show: false,
        },
        emphasis: {
          scale: 1.5,
          label: {
            show: true,
            color: '#ffffff',
            fontSize: 10,
            formatter: '{b}',
          },
        },
      },
      // 订单飞线
      {
        name: '订单流向',
        type: 'lines',
        coordinateSystem: 'geo',
        zlevel: 2,
        large: true,
        effect: {
          show: true,
          constantSpeed: 30,
          symbol: 'arrow',
          symbolSize: 6,
          trailLength: 0.1,
          color: '#00d4ff',
        },
        lineStyle: {
          color: (params) => {
            const flight = mapState.orderFlights[params.dataIndex]
            return flight.status === 'shipping' ? '#00ff88' : '#66ccff'
          },
          width: 2,
          opacity: 0.8,
          curveness: 0.3,
        },
        data: mapState.orderFlights.map((flight) => ({
          fromName: flight.fromName,
          toName: flight.toName,
          coords: [flight.fromCoord, flight.toCoord],
          value: flight.amount,
          lineStyle: {
            color: flight.status === 'shipping' ? '#00ff88' : '#66ccff',
            width: flight.status === 'shipping' ? 3 : 2,
          },
          effect: {
            color: flight.status === 'shipping' ? '#00ff88' : '#66ccff',
            symbolSize: flight.status === 'shipping' ? 8 : 6,
          },
        })),
        tooltip: {
          formatter: (params) => {
            const flight = mapState.orderFlights[params.dataIndex]
            return `
              <div style="padding: 8px;">
                <div style="font-weight: bold; color: #00d4ff; margin-bottom: 4px;">订单详情</div>
                <div style="color: #ffffff; font-size: 12px; line-height: 1.4;">
                  <div>订单号：${flight.orderNo}</div>
                  <div>起点：${flight.fromName}</div>
                  <div>终点：${flight.toName}</div>
                  <div>数量：${flight.amount}</div>
                  <div>状态：${
                    flight.status === 'shipping' ? '运输中' : '已送达'
                  }</div>
                </div>
              </div>
            `
          },
        },
      },
    ],
  }
}

/* =================== 实时产量监控 =================== */
const productionMonitorState = reactive({ option: {} })

const buildProductionMonitorOption = () => {
  // 使用数据服务获取当前基地的24小时产量监控数据
  const hourlyData = dataService.getCurrentBaseHourlyProductionData()
  const hours = hourlyData.hours
  const productionData = hourlyData.actualProduction
  const targetData = hourlyData.targetProduction

  productionMonitorState.option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      textStyle: {
        color: '#fff',
      },
      formatter: function (params) {
        let result = `<div style="color: #00d4ff; font-weight: bold;">${params[0].axisValue}</div>`
        params.forEach((param) => {
          result += `<div style="color: ${param.color};">${param.seriesName}: ${param.value}吨</div>`
        })
        return result
      },
    },
    legend: {
      data: ['实际产量', '目标产量'],
      bottom: '2%',
      textStyle: {
        color: '#aaddff',
        fontSize: 10,
      },
    },
    grid: {
      left: '4%',
      right: '3%',
      top: '12%',
      bottom: '12%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 212, 255, 0.3)',
        },
      },
      axisLabel: {
        color: '#aaddff',
        fontSize: 9,
        interval: 3, // 每4小时显示一个标签
      },
      axisTick: {
        lineStyle: {
          color: 'rgba(0, 212, 255, 0.3)',
        },
      },
    },
    yAxis: {
      type: 'value',
      name: '产量(吨)',
      nameTextStyle: {
        color: '#aaddff',
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 212, 255, 0.3)',
        },
      },
      axisLabel: {
        color: '#aaddff',
        fontSize: 9,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 212, 255, 0.1)',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '实际产量',
        type: 'line',
        data: productionData,
        smooth: true,
        lineStyle: {
          color: '#00ff88',
          width: 3,
        },
        itemStyle: {
          color: '#00ff88',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 255, 136, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 136, 0.05)',
              },
            ],
          },
        },
        markPoint: {
          data: [
            {
              type: 'max',
              name: '最高产量',
              itemStyle: {
                color: '#00ff88',
              },
              label: {
                color: '#00ff88',
                fontSize: 10,
              },
            },
          ],
        },
      },
      {
        name: '目标产量',
        type: 'line',
        data: targetData,
        lineStyle: {
          color: '#00d4ff',
          width: 2,
          type: 'dashed',
        },
        itemStyle: {
          color: '#00d4ff',
        },
        symbol: 'none',
      },
    ],
  }
}

onMounted(() => {
  buildProductionStatsOption()
  processMapOption()
  buildProductionMonitorOption()
})

// 监听选中基地的变化，重新构建图表
watch(
  () => dataService.globalData.selectedBaseId,
  () => {
    // 重新构建所有图表选项
    buildProductionStatsOption()
    buildProductionMonitorOption()

    // 重新获取地图数据
    Object.assign(mapState, {
      ...dataService.getCurrentBaseMapData(),
      orderFlights: dataService.getCurrentBaseOrderFlights(),
    })
    processMapOption()
  },
  { immediate: false },
)
</script>

<style lang="less" scoped>
.base-right-content {
  .data-header {
    display: flex;
    align-items: center;
    height: 42px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.15),
      rgba(0, 212, 255, 0.05)
    );
    backdrop-filter: blur(10px);

    .icon {
      font-size: 45px;
      color: #00d4ff;
      filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
    }

    .title {
      color: #75d1f0;
      font-size: 26px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .data-card-body {
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: auto;
    padding: 8px;
    position: relative;
  }

  // 产品产量统计样式
  .production-stats {
    .data-card-body {
      padding: 8px;
    }
  }

  .production-stats-wrapper {
    height: 280px;
    min-height: 260px;
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.1);
    transition: all 0.3s ease;

    > * {
      width: 100% !important;
      height: 100% !important;
    }
  }

  // 基地分布地图样式
  .base-map {
    .data-card-body {
      padding: 8px;
    }
  }

  .base-map-container {
    height: 260px;
    min-height: 240px;
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(
        circle,
        rgba(0, 212, 255, 0.03) 0%,
        transparent 70%
      );
      animation: mapPulse 8s ease-in-out infinite;
      pointer-events: none;
    }

    > * {
      width: 100% !important;
      height: 100% !important;
      position: relative;
      z-index: 1;
    }
  }

  // 实时产量监控样式
  .production-monitor {
    .data-card-body {
      padding: 8px;
    }
  }

  .production-chart-wrapper {
    height: 270px;
    min-height: 250px;
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.1);
    transition: all 0.3s ease;

    > * {
      width: 100% !important;
      height: 100% !important;
    }
  }

  // 今日产量巨屏
  .today-production {
    background: linear-gradient(
      135deg,
      rgba(0, 255, 136, 0.2),
      rgba(0, 255, 136, 0.05),
      rgba(0, 212, 255, 0.1)
    );
    border: 3px solid rgba(0, 255, 136, 0.4);
    border-radius: 20px;
    padding: 24px;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(0, 255, 136, 0.3),
        transparent
      );
      animation: shimmer 3s infinite;
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(
        circle,
        rgba(0, 255, 136, 0.1) 0%,
        transparent 70%
      );
      transform: translate(-50%, -50%);
      animation: pulse-bg 4s ease-in-out infinite;
    }

    .production-label {
      color: rgba(0, 255, 136, 0.9);
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      text-transform: uppercase;
      letter-spacing: 2px;
      position: relative;
      z-index: 1;
    }

    .production-value {
      display: flex;
      align-items: baseline;
      justify-content: center;
      gap: 12px;
      margin-bottom: 16px;
      position: relative;
      z-index: 1;

      .super-number {
        font-size: 48px !important;
        font-weight: 900 !important;
        color: #00ff88 !important;
        text-shadow: 0 0 30px rgba(0, 255, 136, 0.8);
        filter: drop-shadow(0 0 15px rgba(0, 255, 136, 1));
      }

      .production-unit {
        font-size: 24px;
        color: #00d4ff;
        font-weight: 700;
        text-shadow: 0 0 10px rgba(0, 212, 255, 0.6);
      }
    }

    .production-target {
      position: relative;
      z-index: 1;

      .target-bar {
        width: 100%;
        height: 12px;
        background: rgba(0, 212, 255, 0.2);
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 8px;
        position: relative;

        .target-fill {
          height: 100%;
          background: linear-gradient(90deg, #00ff88, #00d4ff, #00ff88);
          border-radius: 6px;
          transition: width 1s ease;
          position: relative;
          overflow: hidden;

          .target-glow {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent,
              rgba(255, 255, 255, 0.4),
              transparent
            );
            animation: glow-sweep 2s infinite;
          }
        }
      }

      .target-text {
        color: #00d4ff;
        font-size: 14px;
        font-weight: 600;
        text-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
      }
    }
  }

  // 生产关键指标
  .production-metrics {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .metric-row {
    display: flex;
    gap: 12px;
  }

  .metric-card {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 12px;
    border-radius: 14px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.05;
      transition: opacity 0.3s ease;
    }

    &.efficiency {
      background: linear-gradient(
        135deg,
        rgba(0, 255, 136, 0.15),
        rgba(0, 255, 136, 0.05)
      );
      border: 1px solid rgba(0, 255, 136, 0.3);

      &::before {
        background: radial-gradient(circle, #00ff88, transparent);
      }
    }

    &.quality {
      background: linear-gradient(
        135deg,
        rgba(0, 212, 255, 0.15),
        rgba(0, 212, 255, 0.05)
      );
      border: 1px solid rgba(0, 212, 255, 0.3);

      &::before {
        background: radial-gradient(circle, #00d4ff, transparent);
      }
    }

    &.energy {
      background: linear-gradient(
        135deg,
        rgba(255, 174, 0, 0.15),
        rgba(255, 174, 0, 0.05)
      );
      border: 1px solid rgba(255, 174, 0, 0.3);

      &::before {
        background: radial-gradient(circle, #ffae00, transparent);
      }
    }

    &.workers {
      background: linear-gradient(
        135deg,
        rgba(138, 43, 226, 0.15),
        rgba(138, 43, 226, 0.05)
      );
      border: 1px solid rgba(138, 43, 226, 0.3);

      &::before {
        background: radial-gradient(circle, #8a2be2, transparent);
      }
    }

    .metric-icon {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      position: relative;
      z-index: 1;
    }

    &.efficiency .metric-icon {
      background: rgba(0, 255, 136, 0.2);
      color: #00ff88;
      box-shadow: 0 0 15px rgba(0, 255, 136, 0.4);
    }

    &.quality .metric-icon {
      background: rgba(0, 212, 255, 0.2);
      color: #00d4ff;
      box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
    }

    &.energy .metric-icon {
      background: rgba(255, 174, 0, 0.2);
      color: #ffae00;
      box-shadow: 0 0 15px rgba(255, 174, 0, 0.4);
    }

    &.workers .metric-icon {
      background: rgba(138, 43, 226, 0.2);
      color: #8a2be2;
      box-shadow: 0 0 15px rgba(138, 43, 226, 0.4);
    }

    .metric-data {
      flex: 1;

      .metric-number {
        display: flex;
        align-items: baseline;
        gap: 4px;
        font-size: 18px;
        font-weight: 700;
        margin-bottom: 4px;

        .metric-symbol {
          font-size: 12px;
          opacity: 0.8;
        }
      }

      .metric-name {
        font-size: 10px;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    &.efficiency .metric-data .metric-number,
    &.efficiency .metric-data .metric-name {
      color: #00ff88;
    }

    &.quality .metric-data .metric-number,
    &.quality .metric-data .metric-name {
      color: #00d4ff;
    }

    &.energy .metric-data .metric-number,
    &.energy .metric-data .metric-name {
      color: #ffae00;
    }

    &.workers .metric-data .metric-number,
    &.workers .metric-data .metric-name {
      color: #8a2be2;
    }

    .metric-trend {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;

      &.up {
        background: rgba(0, 255, 136, 0.2);
        color: #00ff88;
      }

      &.down {
        background: rgba(255, 174, 0, 0.2);
        color: #ffae00;
      }

      &.stable {
        background: rgba(0, 212, 255, 0.2);
        color: #00d4ff;
      }
    }
  }

  // 关键指标网格
  .metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 12px;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.1;
      transition: opacity 0.3s ease;
    }

    &.critical {
      background: linear-gradient(
        135deg,
        rgba(0, 255, 136, 0.15),
        rgba(0, 255, 136, 0.05)
      );
      border: 1px solid rgba(0, 255, 136, 0.3);

      &::before {
        background: radial-gradient(circle, #00ff88, transparent);
      }
    }

    &.warning {
      background: linear-gradient(
        135deg,
        rgba(255, 174, 0, 0.15),
        rgba(255, 174, 0, 0.05)
      );
      border: 1px solid rgba(255, 174, 0, 0.3);

      &::before {
        background: radial-gradient(circle, #ffae00, transparent);
      }
    }

    &.info {
      background: linear-gradient(
        135deg,
        rgba(0, 212, 255, 0.15),
        rgba(0, 212, 255, 0.05)
      );
      border: 1px solid rgba(0, 212, 255, 0.3);

      &::before {
        background: radial-gradient(circle, #00d4ff, transparent);
      }
    }

    &.success {
      background: linear-gradient(
        135deg,
        rgba(0, 255, 136, 0.15),
        rgba(0, 255, 136, 0.05)
      );
      border: 1px solid rgba(0, 255, 136, 0.3);

      &::before {
        background: radial-gradient(circle, #00ff88, transparent);
      }
    }

    .metric-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      position: relative;
      z-index: 1;
    }

    &.critical .metric-icon {
      background: rgba(0, 255, 136, 0.2);
      color: #00ff88;
      box-shadow: 0 0 15px rgba(0, 255, 136, 0.4);
    }

    &.warning .metric-icon {
      background: rgba(255, 174, 0, 0.2);
      color: #ffae00;
      box-shadow: 0 0 15px rgba(255, 174, 0, 0.4);
    }

    &.info .metric-icon {
      background: rgba(0, 212, 255, 0.2);
      color: #00d4ff;
      box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
    }

    &.success .metric-icon {
      background: rgba(0, 255, 136, 0.2);
      color: #00ff88;
      box-shadow: 0 0 15px rgba(0, 255, 136, 0.4);
    }

    .metric-content {
      flex: 1;

      .metric-value {
        display: flex;
        align-items: baseline;
        gap: 4px;
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 4px;

        .metric-unit {
          font-size: 12px;
          opacity: 0.8;
        }
      }

      .metric-label {
        font-size: 11px;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    &.critical .metric-content .metric-value,
    &.critical .metric-content .metric-label {
      color: #00ff88;
    }

    &.warning .metric-content .metric-value,
    &.warning .metric-content .metric-label {
      color: #ffae00;
    }

    &.info .metric-content .metric-value,
    &.info .metric-content .metric-label {
      color: #00d4ff;
    }

    &.success .metric-content .metric-value,
    &.success .metric-content .metric-label {
      color: #00ff88;
    }

    .metric-status {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      animation: pulse 2s infinite;

      &.online {
        background: #00ff88;
        box-shadow: 0 0 10px rgba(0, 255, 136, 0.6);
      }

      &.fault {
        background: #ffae00;
        box-shadow: 0 0 10px rgba(255, 174, 0, 0.6);
      }

      &.maintenance {
        background: #00d4ff;
        box-shadow: 0 0 10px rgba(0, 212, 255, 0.6);
      }

      &.excellent {
        background: #00ff88;
        box-shadow: 0 0 10px rgba(0, 255, 136, 0.6);
      }
    }
  }

  // 动画效果
  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  @keyframes pulse-bg {
    0%,
    100% {
      opacity: 0.1;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 0.2;
      transform: translate(-50%, -50%) scale(1.1);
    }
  }

  @keyframes glow-sweep {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes mapPulse {
    0%,
    100% {
      transform: scale(1) rotate(0deg);
      opacity: 0.2;
    }
    50% {
      transform: scale(1.1) rotate(180deg);
      opacity: 0.4;
    }
  }
}
</style>
