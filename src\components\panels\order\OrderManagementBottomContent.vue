<template>
  <div class="bottom-content">
    <!-- 左侧区域 -->
    <div class="bottom-left">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig1">
          <i class="i carbon:inventory-management icon"></i>
        </decoFrameB2>
        <span class="title">库存预警</span>
      </div>

      <!-- 卡片包裹区 -->
      <div class="content-card">
        <!-- 库存表格 -->
        <div class="inv-table">
          <!-- 表头 -->
          <div class="inv-row header">
            <span class="cell name">物料</span>
            <span class="cell stock">库存</span>
            <span class="cell safe">安全库存</span>
            <span class="cell status">状态</span>
          </div>
          <!-- 内容 -->
          <div class="inv-body-wrapper" ref="invWrapperRef">
            <div class="inv-list">
              <div
                class="inv-row"
                v-for="item in inventoryAlerts"
                :key="item.id"
                :class="item.level"
              >
                <span class="cell name">{{ item.name }}</span>
                <span class="cell stock">{{ item.stock }}</span>
                <span class="cell safe">{{ item.safe }}</span>
                <span class="cell status">{{
                  item.level === 'danger' ? '危险' : '警告'
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="bottom-right">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig1">
          <i class="i carbon:chart-line-data icon"></i>
        </decoFrameB2>
        <span class="title">同比 / 环比增长</span>
      </div>
      <div class="content-card trend-card">
        <div class="trend-chart-wrapper">
          <echartsInit :chartOption="trendState.option" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

// decoFrameB2 配置
const decoFrameConfig1 = {
  directionAlt: true,
  scale: 0.6,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 36,
}

/* =================== 库存预警数据 - 使用数据服务 =================== */
const inventoryAlerts = reactive(
  dataService.getOrderManagementData().inventoryAlerts,
)

// 根据安全库存计算预警级别
inventoryAlerts.forEach((i) => {
  const ratio = i.stock / i.safe
  i.level = ratio < 0.3 ? 'danger' : 'warning'
})

/* =================== 同比 / 环比趋势 =================== */
const trendState = reactive({ option: {} })

const buildTrendOption = () => {
  // 使用数据服务获取趋势数据
  const trendData = dataService.getOrderManagementData().trendData
  const months = trendData.months
  const yoy = trendData.yoy // 同比 %
  const mom = trendData.mom // 环比 %

  trendState.option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const p1 = params[0]
        const p2 = params[1]
        return `${p1.axisValue}<br/>同比: ${p1.data}%<br/>环比: ${p2.data}%`
      },
    },
    legend: {
      data: ['同比', '环比'],
      textStyle: { color: '#aaddff' },
      top: 0,
    },
    grid: { left: 20, right: 20, top: 20, bottom: 20 },
    xAxis: {
      type: 'category',
      data: months,
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#aaddff', fontSize: 10 },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      splitLine: { lineStyle: { color: 'rgba(0,212,255,0.1)' } },
      axisLabel: { color: '#aaddff' },
      name: '%',
    },
    series: [
      {
        name: '同比',
        type: 'line',
        smooth: true,
        data: yoy,
        itemStyle: { color: '#00d4ff' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0,212,255,0.4)' },
            { offset: 1, color: 'rgba(0,212,255,0)' },
          ]),
        },
      },
      {
        name: '环比',
        type: 'line',
        smooth: true,
        data: mom,
        itemStyle: { color: '#66ccff' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(102,204,255,0.4)' },
            { offset: 1, color: 'rgba(102,204,255,0)' },
          ]),
        },
      },
    ],
  }
}

onMounted(() => {
  buildTrendOption()
})
</script>

<style lang="less" scoped>
.bottom-content {
  display: flex;
  gap: 16px;

  .bottom-left {
    flex: 2;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 0 2px;
  }

  .bottom-right {
    flex: 2;
    gap: 16px;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .data-title {
    display: flex;
    align-items: center;
    height: 36px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.15),
      rgba(0, 212, 255, 0.05)
    );
    backdrop-filter: blur(10px);

    .icon {
      font-size: 45px;
      color: #00d4ff;
      filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
    }

    .title {
      color: #75d1f0;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: 0.5px;
      margin-left: -8px;
    }
  }

  /* 通用内容卡片样式 */
  .content-card {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.06),
      rgba(0, 212, 255, 0.03)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 14px;
    padding: 12px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 14px rgba(0, 212, 255, 0.08);
    flex: 1;
  }

  .inv-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .inv-row {
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 14px;
      color: #aaddff;

      &.header {
        background: rgba(0, 212, 255, 0.08);
        font-weight: 600;
      }

      &.warning {
        background: rgba(255, 174, 0, 0.1);
      }

      &.danger {
        background: rgba(255, 76, 76, 0.15);
      }

      .cell {
        padding: 0 8px;
        white-space: nowrap;

        &.name {
          flex: 2;
        }
        &.stock,
        &.safe {
          flex: 1;
          text-align: center;
        }
        &.status {
          flex: 1;
          text-align: center;
          font-weight: 600;
        }
      }
    }

    /* 滚动容器，用于后期做无缝滚动 */
    .inv-body-wrapper {
      flex: 1;
      overflow-y: auto;
    }
  }

  .trend-card {
    display: flex;
    flex-direction: column;
  }

  .trend-chart-wrapper {
    flex: 1;
    height: 100%;

    /* 让内部 echartsInit 也填满 */
    > * {
      width: 100% !important;
      height: 100% !important;
    }
  }
}
</style>
