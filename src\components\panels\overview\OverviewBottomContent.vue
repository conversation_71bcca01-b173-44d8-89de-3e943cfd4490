<script setup>
import { ref } from 'vue'
import DhtmlxGantt from '@/components/DhtmlxGantt.vue'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

// 锂电铜箔排产任务数据（每条产线一行，包含多个任务） - 使用数据服务
const tasks = ref(dataService.getGanttTasksData())

// 任务点击事件处理
const handleTaskClick = (task) => {
  console.log(
    '点击任务:',
    task.text,
    '进度:',
    Math.round(task.progress * 100) + '%',
  )
  // 这里可以添加任务详情弹窗等功能
}

// 任务双击事件处理
const handleTaskDblClick = (task) => {
  console.log('双击任务:', task.text)
  // 这里可以添加任务编辑弹窗等功能
}

// 任务更新事件处理
const handleTaskUpdated = (task) => {
  console.log(
    '任务更新:',
    task.text,
    '新进度:',
    Math.round(task.progress * 100) + '%',
  )
  // 这里可以同步更新到后端
}
</script>

<template>
  <div class="gantt-container">
    <DhtmlxGantt
      :tasks="tasks"
      @task-click="handleTaskClick"
      @task-dblclick="handleTaskDblClick"
      @task-updated="handleTaskUpdated"
    />
  </div>
</template>
<style lang="less" scoped>
.gantt-container {
  height: 100%;
  width: 100%;
}
</style>
