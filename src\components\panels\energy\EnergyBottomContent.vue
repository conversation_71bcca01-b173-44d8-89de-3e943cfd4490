<template>
  <div class="bottom-content">
    <!-- 左侧：车间利用率 / 变压器负载率 -->
    <div class="left">
      <!-- 左侧标题 -->
      <div class="data-header">
        <decoFrameB2 :config="decoConfig">
          <i class="i carbon:dashboard icon"></i>
        </decoFrameB2>
        <span class="title">车间利用率 / 变压器负载率</span>
      </div>
      <div
        class="eff-card"
        v-for="(card, cIdx) in gaugeState.cards"
        :key="cIdx"
      >
        <div class="gauge-wrap">
          <echartsInit :chartOption="card.option" />
          <div class="gauge-center">{{ card.value }}%</div>
          <div class="gauge-label">{{ card.title }}</div>
        </div>
        <div class="dept-wrap">
          <div class="dept-item" v-for="(d, dIdx) in card.depts" :key="dIdx">
            <span class="d-name">{{ d.name }}</span>
            <div class="d-bar-bg">
              <div class="d-bar-fg" :style="{ width: d.rate + '%' }"></div>
            </div>
            <span class="d-rate">{{ d.rate }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧：峰谷时段用电柱状图 -->
    <div class="right">
      <div class="data-header">
        <decoFrameB2 :config="decoConfig">
          <i class="i carbon:chart-histogram icon"></i>
        </decoFrameB2>
        <span class="title">峰谷时段用电</span>
      </div>
      <div class="bar-body">
        <echartsInit :chartOption="peakState.option" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import * as echarts from 'echarts'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

const decoConfig = {
  directionAlt: true,
  scale: 0.6,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 32,
}

/* ========== 圆环仪表数据 - 使用数据服务 ========= */
const gaugeState = reactive({
  cards: dataService.getEnergyData().gaugeCards.map((card) => ({
    ...card,
    option: {},
  })),
})

const buildGaugeOption = (card) => {
  return {
    series: [
      {
        type: 'gauge',
        startAngle: 90,
        endAngle: -270,
        radius: '100%',
        progress: {
          show: true,
          width: 10,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: card.color[0] },
              { offset: 1, color: card.color[1] },
            ]),
          },
        },
        axisLine: {
          lineStyle: { width: 10, color: [[1, 'rgba(255,255,255,0.05)']] },
        },
        splitLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        pointer: { show: false },
        detail: { show: false },
        data: [{ value: card.value }],
      },
    ],
  }
}

gaugeState.cards.forEach((c) => (c.option = buildGaugeOption(c)))

/* ========== 峰谷时段柱状图 ========= */
const peakState = reactive({ option: {} })

const buildPeakOption = () => {
  // 使用数据服务获取峰谷时段用电数据
  const rawData = dataService.getEnergyData().peakValleyData

  // 按阈值拆分为谷、平、峰三段，用于堆叠柱图
  const valley = rawData.map((v) => (v <= 1000 ? v : 0))
  const flat = rawData.map((v) => (v > 1000 && v <= 3000 ? v : 0))
  const peak = rawData.map((v) => (v > 3000 ? v : 0))

  peakState.option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff', fontSize: 14 },
      formatter: (params) => {
        const p = params.filter((i) => i.data > 0)[0] || params[0]
        const period = p.seriesName
        return `${p.axisValue} 时\n${period}: ${p.data} kWh`
      },
    },
    legend: {
      data: ['谷', '平', '峰'],
      textStyle: { color: '#aaddff' },
      top: 0,
    },
    grid: { left: 40, right: 20, top: 40, bottom: 30 },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => i),
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#aaddff', fontSize: 10 },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      splitLine: { lineStyle: { color: 'rgba(0,212,255,0.1)' } },
      axisLabel: { color: '#aaddff', fontSize: 10 },
    },
    series: [
      {
        name: '谷',
        type: 'bar',
        stack: 'total',
        data: valley,
        barWidth: '60%',
        barBorderRadius: [4, 4, 0, 0],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#006dff' },
            { offset: 1, color: 'rgba(0,109,255,0)' },
          ]),
        },
      },
      {
        name: '平',
        type: 'bar',
        stack: 'total',
        data: flat,
        barWidth: '60%',
        barBorderRadius: [4, 4, 0, 0],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00d4ff' },
            { offset: 1, color: '#0099cc' },
          ]),
        },
      },
      {
        name: '峰',
        type: 'bar',
        stack: 'total',
        data: peak,
        barWidth: '60%',
        barBorderRadius: [4, 4, 0, 0],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ff7600' },
            { offset: 1, color: '#ffd200' },
          ]),
        },
      },
    ],
  }
}

buildPeakOption()
</script>

<style lang="less" scoped>
.bottom-content {
  display: flex;
  gap: 10px;
  height: 100%;

  .left {
    flex: 2;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .data-header {
      display: flex;
      align-items: center;
      height: 36px;
      width: 100%;
      background: linear-gradient(
        135deg,
        rgba(0, 212, 255, 0.15),
        rgba(0, 212, 255, 0.05)
      );
      backdrop-filter: blur(10px);

      .icon {
        font-size: 30px;
        color: #00d4ff;
      }

      .title {
        color: #75d1f0;
        font-size: 20px;
        font-weight: 600;
      }
    }

    .eff-card {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 10px;
      background: linear-gradient(
        145deg,
        rgba(0, 212, 255, 0.05),
        rgba(0, 212, 255, 0.02)
      );
      border: 1px solid rgba(0, 212, 255, 0.25);
      border-radius: 8px;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='104' viewBox='0 0 120 104'%3E%3Cdefs%3E%3Cpattern id='hexBtm' width='60' height='34.64' patternUnits='userSpaceOnUse'%3E%3Cpath d='M30 0 L60 17.32 L60 51.96 L30 69.28 L0 51.96 L0 17.32 Z' fill='none' stroke='%2300d4ff' stroke-opacity='0.06'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='120' height='104' fill='url(%23hexBtm)'/%3E%3C/svg%3E");
        background-size: 120px 104px;
        pointer-events: none;
      }

      .gauge-wrap {
        flex: 0 0 45%;
        min-width: 160px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .gauge-center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 32px;
          color: #ffffff;
          font-weight: 700;
        }
        .gauge-label {
          position: absolute;
          top: 72%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 14px;
          color: #aaddff;
          white-space: nowrap;
          text-align: center;
        }
      }

      .dept-wrap {
        flex: 1;
        padding: 12px 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 6px;

        .dept-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: #ffffff;

          .d-name {
            width: 72px;
            text-align: right;
            flex-shrink: 0;
          }
          .d-bar-bg {
            flex: 1;
            height: 6px;
            background: rgba(0, 212, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            .d-bar-fg {
              height: 100%;
              background: linear-gradient(90deg, #00d4ff 0%, #0099cc 100%);
            }
          }
          .d-rate {
            width: 40px;
          }
        }
      }
    }
  }

  .right {
    flex: 1.2;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .data-header {
      display: flex;
      align-items: center;
      height: 36px;
      background: linear-gradient(
        135deg,
        rgba(0, 212, 255, 0.15),
        rgba(0, 212, 255, 0.05)
      );
      backdrop-filter: blur(10px);

      .icon {
        font-size: 30px;
        color: #00d4ff;
      }

      .title {
        color: #75d1f0;
        font-size: 20px;
        font-weight: 600;
      }
    }

    .bar-body {
      flex: 1;
      position: relative;
      background: linear-gradient(
        145deg,
        rgba(0, 212, 255, 0.05),
        rgba(0, 212, 255, 0.02)
      );
      border: 1px solid rgba(0, 212, 255, 0.25);
      border-radius: 8px;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='104' viewBox='0 0 120 104'%3E%3Cdefs%3E%3Cpattern id='hexBtm2' width='60' height='34.64' patternUnits='userSpaceOnUse'%3E%3Cpath d='M30 0 L60 17.32 L60 51.96 L30 69.28 L0 51.96 L0 17.32 Z' fill='none' stroke='%2300d4ff' stroke-opacity='0.06'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='120' height='104' fill='url(%23hexBtm2)'/%3E%3C/svg%3E");
        background-size: 120px 104px;
        pointer-events: none;
      }
    }
  }
}
</style>
