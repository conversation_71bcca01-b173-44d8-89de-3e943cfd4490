{"name": "techui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.4.0", "ayin-color": "^1.0.9", "ayin-lessmixins": "^1.0.7", "dhtmlx-gantt": "^9.0.13", "echarts": "^5.4.2", "mockjs": "^1.1.0", "techui-vue3-lite": "^3.0.7", "three": "^0.158.0", "vue": "^3.2.47", "vue-router": "^4.1.6", "vuex": "^4.1.0"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.3", "@vitejs/plugin-vue": "^4.1.0", "less": "^4.1.3", "less-loader": "^11.1.0", "vite": "^4.3.9"}}