<template>
  <div class="right-content">
    <!-- 全国基地总览 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:earth-southeast-asia icon"></i>
        </decoFrameB2>
        <span class="title">全国基地总览</span>
      </div>
      <div class="data-card-body">
        <div class="screenA-counterGrid">
          <aYinTechBorderB3
            :config="borderConfig(index)"
            v-for="(item, index) in globalOverviewData.arry"
            :key="index"
          >
            <div class="inner-content">
              <div class="block-title">
                {{ item.title }} <span v-if="item.unit">({{ item.unit }})</span>
              </div>
              <div class="total">
                <i :class="[item.icon, 'icon']"></i>
                <DigitalTransform
                  class="numbers"
                  :value="item.total"
                  :useGrouping="true"
                  :interval="1000"
                />
              </div>
            </div>
          </aYinTechBorderB3>
        </div>
        <!-- 基地产能对比 -->
        <div class="production-chart-card">
          <div class="chart-header">
            <i class="i carbon:chart-bar chart-icon"></i>
            <span class="chart-title">各基地产能对比</span>
          </div>
          <div class="chart-container">
            <echartsInit :chartOption="baseComparisonState.chartOption" />
          </div>
        </div>
      </div>
    </div>

    <!-- 基地效率排名 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:trophy icon"></i>
        </decoFrameB2>
        <span class="title">基地效率排名</span>
      </div>
      <div class="data-card-body">
        <div class="efficiency-ranking">
          <div
            class="ranking-item"
            v-for="(base, index) in baseEfficiencyRanking"
            :key="base.name"
            :class="{ 'top-performer': index < 2 }"
          >
            <div class="rank-badge">
              <span class="rank-number">{{ index + 1 }}</span>
            </div>
            <div class="base-info">
              <div class="base-name">{{ base.name }}</div>
            </div>
            <div class="efficiency-data">
              <div class="efficiency-value">
                <DigitalTransform
                  :value="base.efficiency"
                  :useGrouping="false"
                  :interval="1500"
                />%
              </div>
              <div class="status-indicator" :class="base.status"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 基地订单流向统计 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:flow-stream icon"></i>
        </decoFrameB2>
        <span class="title">基地订单流向统计</span>
      </div>
      <div class="data-card-body">
        <!-- 订单流向进度条展示 -->
        <div class="order-flow-progress">
          <div
            class="progress-item"
            v-for="(item, index) in orderFlowStats"
            :key="index"
          >
            <div class="progress-header">
              <div class="base-info">
                <i :class="['i', item.icon, 'base-icon']"></i>
                <span class="base-name">{{ item.baseName }}</span>
              </div>
              <div class="progress-value">
                <DigitalTransform
                  :value="item.percentage"
                  :useGrouping="false"
                  :interval="1200"
                />%
              </div>
            </div>
            <div class="progress-bar-container">
              <div
                class="progress-bar"
                :style="{ width: item.percentage + '%' }"
                :class="'progress-' + (index + 1)"
              ></div>
            </div>
            <div class="progress-details">
              <span class="order-info">{{ item.orderCount }}单</span>
              <span class="amount-info">{{ item.totalAmount }}吨</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import * as echarts from 'echarts'
import chinaJson from '@/utils/map/中华人民共和国.json'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

// decoFrameB2 配置
const decoFrameConfig = {
  directionAlt: true,
  scale: 0.8,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 40,
}

// 基地订单流向统计数据 - 使用数据服务
const orderFlowStats = reactive(dataService.getOrderFlowStats())

// 铜箔生产原料消耗数据状态 - 使用数据服务
const mediumState = reactive({
  usageData: dataService.getMaterialsData(),
})

// 本周铜箔排产完成情况数据状态 - 使用数据服务
const productionState = reactive({
  chartData: dataService.getWeeklyProductionData(),
  chartOption: {},
})

// 基地分布地图数据状态 - 使用数据服务
const mapState = reactive({
  chartOption: {},
  ...dataService.getMapData(),
  // 订单飞线数据 - 使用数据服务
  orderFlights: dataService.getOrderFlightsData(),
})

// 全国基地总览数据 - 使用数据服务
const globalOverviewData = reactive(dataService.getGlobalOverviewData())

// 基地产能对比数据 - 使用数据服务
const baseComparisonState = reactive({
  chartData: dataService.getBaseComparisonData(),
  chartOption: {},
})

// 基地效率排名数据 - 使用数据服务
const baseEfficiencyRanking = reactive(dataService.getBaseEfficiencyRanking())

// 排产执行总览数据 - 使用数据服务
const state1 = reactive(dataService.getSchedulingOverviewData())

const borderConfig = (index) => {
  let rotate = null
  if (index === 0) rotate = 'x'
  else if (index === 1) rotate = 'all'
  else if (index === 3) rotate = 'y'
  return { dur: 3, opacity: 0.7, rotate }
}

// 配置基地产能对比图表
const processBaseComparisonOption = () => {
  baseComparisonState.chartOption = {
    update: false,
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = params[0].name + '<br/>'
        params.forEach((param) => {
          result +=
            param.marker + param.seriesName + ': ' + param.value + '吨<br/>'
        })
        if (params.length >= 2) {
          const efficiency = (
            (params[1].value / params[0].value) *
            100
          ).toFixed(1)
          result += '产能利用率: ' + efficiency + '%'
        }
        return result
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff', fontSize: 12 },
    },
    legend: {
      data: ['设计产能', '实际产量'],
      textStyle: { color: '#00d4ff', fontSize: 11 },
      top: '5%',
      right: '5%',
    },
    grid: {
      left: '0%',
      right: '6%',
      top: '20%',
      bottom: '0%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: baseComparisonState.chartData.bases,
      axisLine: { lineStyle: { color: '#00d4ff', width: 1 } },
      axisLabel: { color: '#00d4ff', fontSize: 11 },
      axisTick: { show: false },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#66ccff', fontSize: 11, formatter: '{value}吨' },
      splitLine: {
        lineStyle: { color: 'rgba(0, 212, 255, 0.1)', type: 'dashed' },
      },
    },
    series: [
      {
        name: '设计产能',
        type: 'bar',
        data: baseComparisonState.chartData.capacity,
        barWidth: '35%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 174, 0, 0.8)' },
              { offset: 1, color: 'rgba(255, 174, 0, 0.4)' },
            ],
          },
          borderRadius: [4, 4, 0, 0],
        },
      },
      {
        name: '实际产量',
        type: 'bar',
        data: baseComparisonState.chartData.actualOutput,
        barWidth: '35%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#00d4ff' },
              { offset: 1, color: '#0099cc' },
            ],
          },
          borderRadius: [4, 4, 0, 0],
        },
        emphasis: {
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#33ddff' },
                { offset: 1, color: '#00aadd' },
              ],
            },
          },
        },
      },
    ],
  }
}

// 配置排产完成情况对比图
const processProductionOption = () => {
  productionState.chartOption = {
    update: false,
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = params[0].name + '<br/>'
        params.forEach((param) => {
          result +=
            param.marker + param.seriesName + ': ' + param.value + '吨<br/>'
        })
        if (params.length >= 2) {
          const completion = (
            (params[1].value / params[0].value) *
            100
          ).toFixed(1)
          result += '完成率: ' + completion + '%'
        }
        return result
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff', fontSize: 12 },
    },
    legend: {
      data: ['计划产量', '实际产量'],
      textStyle: { color: '#00d4ff', fontSize: 11 },
      top: '5%',
      right: '5%',
    },
    grid: {
      left: '0%',
      right: '6%',
      top: '20%',
      bottom: '0%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: productionState.chartData.categories,
      axisLine: { lineStyle: { color: '#00d4ff', width: 1 } },
      axisLabel: { color: '#00d4ff', fontSize: 11 },
      axisTick: { show: false },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#66ccff', fontSize: 11, formatter: '{value}吨' },
      splitLine: {
        lineStyle: { color: 'rgba(0, 212, 255, 0.1)', type: 'dashed' },
      },
    },
    series: [
      {
        name: '计划产量',
        type: 'bar',
        data: productionState.chartData.planned,
        barWidth: '35%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 174, 0, 0.8)' },
              { offset: 1, color: 'rgba(255, 174, 0, 0.4)' },
            ],
          },
          borderRadius: [4, 4, 0, 0],
        },
      },
      {
        name: '实际产量',
        type: 'bar',
        data: productionState.chartData.actual,
        barWidth: '35%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#00d4ff' },
              { offset: 1, color: '#0099cc' },
            ],
          },
          borderRadius: [4, 4, 0, 0],
        },
        emphasis: {
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#33ddff' },
                { offset: 1, color: '#00aadd' },
              ],
            },
          },
        },
      },
    ],
  }
}

// 配置基地分布地图
const processMapOption = () => {
  // 注册中国地图
  echarts.registerMap('china', chinaJson)

  mapState.chartOption = {
    update: false,
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if (
          params.componentType === 'series' &&
          params.seriesType === 'scatter'
        ) {
          const data = params.data
          const baseInfo = mapState.baseData.find(
            (base) => base.name === data.name,
          )
          if (baseInfo) {
            return `
              <div style="padding: 8px;">
                <div style="font-weight: bold; color: #00d4ff; margin-bottom: 6px;">${
                  baseInfo.name
                }</div>
                <div style="color: #ffffff; font-size: 12px; line-height: 1.5;">
                  <div>位置：${baseInfo.location}</div>
                  <div>产能：${baseInfo.capacity}</div>
                  <div>员工：${baseInfo.employees}人</div>
                  <div>状态：${
                    baseInfo.status === 'running' ? '正常运行' : '维护中'
                  }</div>
                  <div style="margin-top: 4px; color: #99ccff;">${
                    baseInfo.description
                  }</div>
                </div>
              </div>
            `
          }
        }
        return params.name
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff' },
    },
    geo: {
      map: 'china',
      roam: true, // 启用缩放和拖拽
      zoom: 2.5,
      center: [104, 35],
      scaleLimit: {
        min: 0.8, // 最小缩放比例
      },
      label: {
        show: false, // 隐藏省份名称
        color: 'rgba(0, 212, 255, 0.6)',
        fontSize: 10,
        fontWeight: 'normal',
      },
      itemStyle: {
        areaColor: 'rgba(0, 59, 92, 0.3)',
        borderColor: 'rgba(0, 212, 255, 0.4)',
        borderWidth: 1,
      },
      emphasis: {
        label: {
          show: false, // 悬浮时也不显示省份名称
          color: '#00d4ff',
          fontSize: 11,
        },
        itemStyle: {
          areaColor: 'rgba(0, 91, 139, 0.5)',
          borderColor: '#00d4ff',
          borderWidth: 2,
        },
      },
    },
    series: [
      {
        name: '基地分布',
        type: 'scatter',
        coordinateSystem: 'geo',
        symbol: 'pin',
        symbolSize: (val) => {
          // 根据产能大小调整图标大小
          const capacity = val[2]
          return Math.max(25, Math.min(45, capacity / 100 + 20))
        },
        data: mapState.baseData.map((base) => ({
          name: base.name,
          value: base.value,
          itemStyle: {
            color: base.status === 'running' ? '#00d4ff' : '#ffae00',
            shadowBlur: 8,
            shadowColor: base.status === 'running' ? '#00d4ff' : '#ffae00',
          },
        })),
        label: {
          show: true,
          position: 'bottom',
          color: '#00d4ff',
          fontSize: 11,
          fontWeight: 'bold',
          formatter: '{b}',
          offset: [0, 5],
        },
        emphasis: {
          scale: 1.2,
          label: {
            show: true,
            color: '#ffffff',
            fontSize: 12,
          },
        },
      },
      // 客户位置标记
      {
        name: '客户位置',
        type: 'scatter',
        coordinateSystem: 'geo',
        symbol: 'circle',
        symbolSize: 8,
        data: mapState.orderFlights.map((flight) => ({
          name: flight.toName,
          value: flight.toCoord,
          itemStyle: {
            color: flight.status === 'shipping' ? '#00ff88' : '#66ccff',
            shadowBlur: 4,
            shadowColor: flight.status === 'shipping' ? '#00ff88' : '#66ccff',
          },
        })),
        label: {
          show: false,
        },
        emphasis: {
          scale: 1.5,
          label: {
            show: true,
            color: '#ffffff',
            fontSize: 10,
            formatter: '{b}',
          },
        },
      },
      // 订单飞线
      {
        name: '订单流向',
        type: 'lines',
        coordinateSystem: 'geo',
        zlevel: 2,
        large: true,
        effect: {
          show: true,
          constantSpeed: 30,
          symbol: 'arrow',
          symbolSize: 6,
          trailLength: 0.1,
          color: '#00d4ff',
        },
        lineStyle: {
          color: (params) => {
            const flight = mapState.orderFlights[params.dataIndex]
            return flight.status === 'shipping' ? '#00ff88' : '#66ccff'
          },
          width: 2,
          opacity: 0.8,
          curveness: 0.3,
        },
        data: mapState.orderFlights.map((flight) => ({
          fromName: flight.fromName,
          toName: flight.toName,
          coords: [flight.fromCoord, flight.toCoord],
          value: flight.amount,
          lineStyle: {
            color: flight.status === 'shipping' ? '#00ff88' : '#66ccff',
            width: flight.status === 'shipping' ? 3 : 2,
          },
          effect: {
            color: flight.status === 'shipping' ? '#00ff88' : '#66ccff',
            symbolSize: flight.status === 'shipping' ? 8 : 6,
          },
        })),
        tooltip: {
          formatter: (params) => {
            const flight = mapState.orderFlights[params.dataIndex]
            return `
              <div style="padding: 8px;">
                <div style="font-weight: bold; color: #00d4ff; margin-bottom: 4px;">订单详情</div>
                <div style="color: #ffffff; font-size: 12px; line-height: 1.4;">
                  <div>订单号：${flight.orderNo}</div>
                  <div>起点：${flight.fromName}</div>
                  <div>终点：${flight.toName}</div>
                  <div>数量：${flight.amount}</div>
                  <div>状态：${
                    flight.status === 'shipping' ? '运输中' : '已送达'
                  }</div>
                </div>
              </div>
            `
          },
        },
      },
    ],
  }
}

onMounted(() => {
  processBaseComparisonOption()
  processProductionOption()
})
</script>

<style lang="less" scoped>
.right-content {
  .data-header {
    display: flex;
    align-items: center;
    height: 42px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.15),
      rgba(0, 212, 255, 0.05)
    );
    backdrop-filter: blur(10px);

    .icon {
      font-size: 45px;
      color: #00d4ff;
      filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
    }

    .title {
      color: #75d1f0;
      font-size: 26px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .data-card-body {
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: auto;
    padding: 8px;
    position: relative;
  }

  // 基地效率排名样式
  .efficiency-ranking {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px 0;
  }

  .ranking-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.15);
    border-radius: 8px;
    backdrop-filter: blur(6px);
    transition: all 0.3s ease;

    &.top-performer {
      border-color: rgba(255, 174, 0, 0.4);
      background: linear-gradient(
        145deg,
        rgba(255, 174, 0, 0.12),
        rgba(255, 174, 0, 0.06)
      );
      box-shadow: 0 2px 12px rgba(255, 174, 0, 0.15);
    }

    .rank-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: linear-gradient(135deg, #00d4ff, #0099cc);
      margin-right: 12px;
      flex-shrink: 0;

      .rank-number {
        color: #ffffff;
        font-size: 16px;
        font-weight: 800;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }

    &.top-performer .rank-badge {
      background: linear-gradient(135deg, #ffae00, #ff8c00);
    }

    .base-info {
      flex: 1;
      margin-right: 12px;

      .base-name {
        color: #00d4ff;
        font-size: 16px;
        font-weight: 700;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }

    .efficiency-data {
      display: flex;
      align-items: center;
      gap: 8px;

      .efficiency-value {
        color: #00d4ff;
        font-size: 20px;
        font-weight: 800;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        flex-shrink: 0;

        &.excellent {
          background: #00ff88;
          box-shadow: 0 0 8px rgba(0, 255, 136, 0.6);
        }

        &.good {
          background: #00d4ff;
          box-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
        }

        &.maintenance {
          background: #ffae00;
          box-shadow: 0 0 8px rgba(255, 174, 0, 0.6);
        }
      }
    }
  }

  // 订单流向进度条样式
  .order-flow-progress {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 4px 0;
  }

  .progress-item {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.15);
    border-radius: 10px;
    padding: 8px;
    backdrop-filter: blur(6px);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(0, 212, 255, 0.3);
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(0, 212, 255, 0.12);
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;

      .base-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .base-icon {
          font-size: 16px;
          color: #00d4ff;
          filter: drop-shadow(0 0 4px rgba(0, 212, 255, 0.4));
        }

        .base-name {
          color: #00d4ff;
          font-size: 14px;
          font-weight: 600;
          letter-spacing: 0.3px;
        }
      }

      .progress-value {
        color: #00d4ff;
        font-size: 16px;
        font-weight: 700;
        text-shadow: 0 0 8px rgba(0, 212, 255, 0.4);
      }
    }

    .progress-bar-container {
      background: rgba(0, 212, 255, 0.1);
      border-radius: 6px;
      height: 8px;
      overflow: hidden;
      margin-bottom: 4px;
      position: relative;

      .progress-bar {
        height: 100%;
        border-radius: 6px;
        transition: width 2s ease-in-out;
        position: relative;
        overflow: hidden;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent
          );
          animation: shimmer 2s infinite;
        }

        &.progress-1 {
          background: linear-gradient(90deg, #00d4ff, #0099cc);
        }

        &.progress-2 {
          background: linear-gradient(90deg, #00ff88, #00cc66);
        }

        &.progress-3 {
          background: linear-gradient(90deg, #ffae00, #ff8800);
        }
      }
    }

    .progress-details {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;

      .order-info {
        color: rgba(0, 212, 255, 0.8);
        font-weight: 600;
      }

      .amount-info {
        color: rgba(0, 212, 255, 0.8);
        font-weight: 600;
      }
    }
  }

  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  // 介质用量统计样式
  .medium-usage-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    box-sizing: border-box;
    width: 100%;
  }

  .usage-card {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.15);
    border-radius: 8px;
    backdrop-filter: blur(6px);
    box-shadow: 0 2px 8px rgba(0, 212, 255, 0.06);
    transition: all 0.2s ease;
    min-height: 60px;
    box-sizing: border-box;
    overflow: hidden;

    .card-content {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      padding: 0 12px;
      width: 100%;

      .left-section {
        display: flex;
        align-items: center;
        gap: 6px;
        flex: 0 0 auto;
        max-width: 60%;

        .medium-icon {
          font-size: 24px;
          color: #00d4ff;
          filter: drop-shadow(0 0 6px rgba(0, 212, 255, 0.4));
          flex-shrink: 0;
        }

        .medium-name {
          color: #00d4ff;
          font-size: 16px;
          font-weight: 700;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .right-section {
        display: flex;
        align-items: baseline;
        gap: 4px;
        flex: 0 0 auto;
        justify-content: flex-end;

        .usage-value {
          color: #00d4ff;
          font-size: 22px;
          font-weight: 800;
          line-height: 1;
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .usage-unit {
          color: rgba(0, 212, 255, 0.8);
          font-size: 14px;
          font-weight: 700;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }

  // 生产量柱状图样式
  .production-chart-card {
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.08),
      rgba(0, 212, 255, 0.04)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    padding: 12px;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.1);
    transition: all 0.3s ease;

    .chart-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(0, 212, 255, 0.15);

      .chart-icon {
        font-size: 18px;
        color: #00d4ff;
        filter: drop-shadow(0 0 6px rgba(0, 212, 255, 0.4));
      }

      .chart-title {
        color: #00d4ff;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 0.3px;
      }
    }

    .chart-container {
      height: 110px;
      position: relative;
    }
  }
}

.screenA-counterGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  height: 145px;
  grid-template-rows: repeat(2, 1fr);
  position: relative;

  .aYinTechBorderB3 {
    padding: 0;
    position: relative;

    .inner-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 8px;

      .block-title {
        font-size: 12px;
        color: #00d4ff;
        margin-bottom: 6px;
        text-align: center;
        line-height: 1.2;
        font-weight: 600;
      }

      .total {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        i {
          font-size: 22px;
          color: #00d4ff;
          flex-shrink: 0;
          filter: drop-shadow(0 2px 4px rgba(0, 212, 255, 0.4));
        }

        .numbers {
          font-size: 22px;
          color: #00d4ff;
          font-weight: 800;
          line-height: 1;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
</style>
