<template>
  <div class="right-content">
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoConfig">
          <i class="i carbon:flash icon"></i>
        </decoFrameB2>
        <span class="title">能源管控</span>
      </div>
      <div class="data-card-body">
        <div class="energy-grid">
          <div class="meter-item" v-for="item in meters" :key="item.label">
            <i :class="['i', item.icon, 'item-icon']"></i>
            <span class="meter-label">{{ item.label }}</span>
            <div class="value-wrap">
              <DigitalTransform :value="item.value" class="meter-value" />
              <span class="meter-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 本周用电量 -->
    <div class="data-card weekly-card">
      <div class="data-header">
        <decoFrameB2 :config="decoConfig">
          <i class="i carbon:chart-column icon"></i>
        </decoFrameB2>
        <span class="title">本周用电量</span>
      </div>
      <div class="data-card-body weekly-body">
        <echartsInit :chartOption="weekState.chartOption" />
      </div>
    </div>

    <!-- 各区用电统计 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoConfig">
          <i class="i carbon:map icon"></i>
        </decoFrameB2>
        <span class="title">各区用电统计</span>
      </div>
      <div class="data-card-body district-grid">
        <aYinTechBorderB1
          v-for="(item, idx) in districtState.usage"
          :key="idx"
          :config="borderConfig(idx)"
          class="d-border"
        >
          <div class="d-card">
            <div class="d-name">{{ item.name }}</div>

            <div class="metric">
              <span class="label">今日</span>
              <DigitalTransform
                class="value"
                :value="item.today"
                :useGrouping="true"
              />
              <span class="unit">kWh</span>
            </div>
            <div class="metric">
              <span class="label">本周</span>
              <DigitalTransform
                class="value"
                :value="item.week"
                :useGrouping="true"
              />
              <span class="unit">kWh</span>
            </div>
            <div class="metric">
              <span class="label">峰值</span>
              <DigitalTransform class="value" :value="item.peak" />
              <span class="unit">MW</span>
            </div>
          </div>
        </aYinTechBorderB1>
      </div>
    </div>
    <!-- 用能效率与对标 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoConfig">
          <i class="i carbon:data-check icon"></i>
        </decoFrameB2>
        <span class="title">用能效率与对标</span>
      </div>
      <div class="data-card-body eff-body eff-grid">
        <aYinTechBorderB3
          v-for="(metric, idx) in effState.metrics"
          :key="idx"
          :config="borderConfig(idx)"
          class="eff-border"
        >
          <div class="eff-card">
            <div class="m-name">{{ metric.name }}</div>
            <div class="m-value-wrap">
              <DigitalTransform class="m-val" :value="metric.value" />
              <span class="m-unit">{{ metric.unit }}</span>
            </div>
            <div class="m-bm">
              目标
              <span class="bm-val">{{ metric.target }}{{ metric.unit }}</span>
              <i
                :class="[
                  'i',
                  metric.diff >= 0 ? 'carbon:arrow-up' : 'carbon:arrow-down',
                  metric.diff >= 0 ? 'up' : 'down',
                ]"
              ></i>
            </div>
          </div>
        </aYinTechBorderB3>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import * as echarts from 'echarts'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

const decoConfig = {
  directionAlt: true,
  scale: 0.8,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 40,
}

// 使用数据服务获取能耗仪表数据
const meters = dataService.getEnergyData().meters

// 本周用电量柱状图数据 - 使用数据服务
const weekState = reactive({
  chartData: dataService.getEnergyData().weeklyPowerConsumption,
  chartOption: {},
})

const buildWeekChartOption = () => {
  const { categories, data } = weekState.chartData
  weekState.chartOption = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff', fontSize: 14 },
      formatter: '{b}: {c} kWh',
    },
    grid: { top: 10, left: 10, right: 10, bottom: 24, containLabel: true },
    xAxis: {
      type: 'category',
      data: categories,
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#aaddff' },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      splitLine: { lineStyle: { color: 'rgba(0, 212, 255, 0.1)' } },
      axisLabel: { color: '#aaddff' },
    },
    series: [
      {
        name: '用电量',
        type: 'bar',
        data,
        barWidth: '45%',
        barBorderRadius: 4,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00d4ff' },
            { offset: 1, color: '#0099cc' },
          ]),
          shadowBlur: 10,
          shadowColor: 'rgba(0, 212, 255, 0.6)',
        },
      },
    ],
  }
}

// border color helper
const borderConfig = (idx) => ({
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  height: 30,
  reverse: idx % 2 === 1,
})

// 区域用电数据 - 使用数据服务
const districtState = reactive({
  usage: dataService.getEnergyData().districtUsage,
})

// 能效与对标数据 - 使用数据服务
const effState = reactive({
  metrics: dataService.getEnergyData().efficiencyMetrics.map((m) => ({
    ...m,
    diff: m.target - m.value,
  })),
})

onMounted(() => {
  buildWeekChartOption()
})
</script>

<style lang="less" scoped>
.right-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  .data-card {
    background: transparent;

    .data-header {
      display: flex;
      align-items: center;
      height: 42px;
      background: linear-gradient(
        135deg,
        rgba(0, 212, 255, 0.15),
        rgba(0, 212, 255, 0.05)
      );
      backdrop-filter: blur(10px);

      .icon {
        font-size: 45px;
        color: #00d4ff;
        filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
      }

      .title {
        color: #75d1f0;
        font-size: 26px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }
    }

    .data-card-body {
      padding: 10px;
    }

    .energy-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;

      .meter-item {
        background: linear-gradient(
          145deg,
          rgba(0, 212, 255, 0.05),
          rgba(0, 212, 255, 0.02)
        );
        border: 1px solid rgba(0, 212, 255, 0.25);
        border-radius: 8px;
        padding: 12px;
        text-align: center;
        position: relative;
        overflow: hidden;

        /* hexagon grid overlay */
        &::before {
          content: '';
          position: absolute;
          inset: 0;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='104' viewBox='0 0 120 104'%3E%3Cdefs%3E%3Cpattern id='hex' width='60' height='34.64' patternUnits='userSpaceOnUse'%3E%3Cpath d='M30 0 L60 17.32 L60 51.96 L30 69.28 L0 51.96 L0 17.32 Z' fill='none' stroke='%2300d4ff' stroke-opacity='0.08'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='120' height='104' fill='url(%23hex)'/%3E%3C/svg%3E");
          background-size: 120px 104px;
          pointer-events: none;
        }

        .item-icon {
          font-size: 24px;
          color: #00d4ff;
          margin-bottom: 4px;
          position: relative;
        }

        .meter-label {
          font-size: 13px;
          color: #aaddff;
          position: relative;
        }

        .value-wrap {
          display: flex;
          align-items: baseline;
          justify-content: center;
          gap: 2px;
          margin-top: 4px;
          position: relative;

          .meter-value {
            font-size: 22px;
            color: #00d4ff;
            font-weight: 700;
          }

          .meter-unit {
            font-size: 12px;
            color: rgba(0, 212, 255, 0.7);
          }
        }
      }
    }
  }
  .weekly-card {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .weekly-body {
    height: 180px;
    position: relative;
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.05),
      rgba(0, 212, 255, 0.02)
    );
    border: 1px solid rgba(0, 212, 255, 0.25);
    border-radius: 8px;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='104' viewBox='0 0 120 104'%3E%3Cdefs%3E%3Cpattern id='hexBg' width='60' height='34.64' patternUnits='userSpaceOnUse'%3E%3Cpath d='M30 0 L60 17.32 L60 51.96 L30 69.28 L0 51.96 L0 17.32 Z' fill='none' stroke='%2300d4ff' stroke-opacity='0.08'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='120' height='104' fill='url(%23hexBg)'/%3E%3C/svg%3E");
      background-size: 120px 104px;
      pointer-events: none;
    }
  }

  .district-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .d-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 10px 6px;
    height: 100%;
    justify-content: center;

    .d-name {
      font-size: 16px;
      color: #00d4ff;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .metric {
      display: flex;
      align-items: baseline;
      gap: 2px;

      .label {
        font-size: 12px;
        color: #aaddff;
        width: 28px; /* 统一列宽 */
        text-align: right;
      }
      .value {
        font-size: 18px;
        color: #00d4ff;
        font-weight: 700;
      }
      .unit {
        font-size: 11px;
        color: rgba(0, 212, 255, 0.8);
      }
    }
  }

  .d-border {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 140px;
  }

  .eff-body {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
    grid-auto-rows: minmax(170px, 1fr);
    gap: 10px;
    align-content: start;
  }

  .eff-border {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .eff-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    height: 100%;
    justify-content: center;

    .m-name {
      color: #00d4ff;
      font-size: 14px;
      font-weight: 600;
    }

    .m-value-wrap {
      display: flex;
      align-items: baseline;
      gap: 2px;

      .m-val {
        font-size: 22px;
        color: #00d4ff;
        font-weight: 700;
      }

      .m-unit {
        font-size: 12px;
        color: rgba(0, 212, 255, 0.8);
      }
    }

    .m-bm {
      font-size: 12px;
      color: #aaddff;
      display: flex;
      align-items: center;
      gap: 2px;

      .bm-val {
        color: #00d4ff;
        font-weight: 600;
      }

      .up {
        color: #ff5252;
      }
      .down {
        color: #00d4ff;
      }
    }
  }
}
</style>
