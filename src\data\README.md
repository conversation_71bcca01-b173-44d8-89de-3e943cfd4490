# 数据统一管理系统

## 📊 概述

本项目采用统一的数据管理方案，将所有数据集中在 `dashboardData.json` 文件中，通过 `useDataService` 组合函数提供统一的数据访问接口。

## 🗂️ 文件结构

```
src/data/
├── dashboardData.json      # 统一数据源
├── README.md              # 使用说明
└── ...

src/composables/
└── useDataService.js      # 数据服务组合函数
```

## 📋 数据结构

### 基地数据 (bases)
```json
{
  "id": "huadong",
  "name": "华东基地",
  "location": "上海市",
  "coordinates": [121.4737, 31.2304],
  "capacity": "3500吨/月",
  "actualOutput": 3150,
  "status": "running",
  "employees": 280,
  "efficiency": 92.5,
  "description": "主要生产高精度锂电铜箔"
}
```

### 全国总览数据 (globalOverview)
- totalCapacity: 全国总产能
- runningBases: 运行基地数
- totalOrders: 总订单量
- averageEfficiency: 平均效率

### 生产线数据 (productionLines)
- 生产线状态、产量、效率、质量等信息

### 其他数据模块
- materials: 原料消耗数据
- orderFlowStats: 订单流向统计
- productionStats: 产品产量统计
- workshopRadar: 车间雷达图数据
- schedulingOverview: 排产执行总览
- weeklyProduction: 本周生产数据
- personnelData: 人员配置数据
- safetyData: 安全指标数据
- logisticsData: 物流数据
- equipmentStatus: 设备状态数据
- qualityTrend: 质量趋势数据

## 🔧 使用方法

### 1. 在组件中导入数据服务

```javascript
import { useDataService } from '@/composables/useDataService'

// 在 setup 函数中使用
const dataService = useDataService()
```

### 2. 获取数据

```javascript
// 获取全国基地总览数据
const globalOverviewData = reactive(dataService.getGlobalOverviewData())

// 获取基地效率排名
const baseRanking = dataService.getBaseEfficiencyRanking()

// 获取当前选中的基地
const currentBase = dataService.getCurrentBase()

// 获取生产线数据
const productionLines = dataService.getProductionLines()
```

### 3. 数据更新

```javascript
// 更新数据
dataService.updateData({
  globalOverview: {
    totalCapacity: "9000",
    runningBases: "4",
    // ...
  }
})

// 切换选中的基地
dataService.setSelectedBase('huanan')
```

## 📝 组件迁移指南

### 替换前（硬编码数据）
```javascript
const globalOverviewData = reactive({
  arry: [
    {
      title: '全国总产能',
      icon: 'i carbon:factory',
      unit: '吨/月',
      total: '8500',
    },
    // ...
  ],
})
```

### 替换后（使用数据服务）
```javascript
import { useDataService } from '@/composables/useDataService'

const dataService = useDataService()
const globalOverviewData = reactive(dataService.getGlobalOverviewData())
```

## 🎯 优势

### 1. 数据集中管理
- 所有数据统一存储在 JSON 文件中
- 便于维护和更新
- 避免数据重复和不一致

### 2. 类型安全
- 统一的数据接口
- 减少数据访问错误
- 更好的代码提示

### 3. 响应式更新
- 数据变化自动更新所有相关组件
- 支持实时数据同步
- 状态管理更简单

### 4. 易于扩展
- 新增数据类型只需修改 JSON 文件
- 数据服务自动适配
- 组件无需修改

## 🔄 数据流

```
dashboardData.json → useDataService → 组件
                         ↑
                    数据更新/状态管理
```

## 📊 已迁移的组件

- ✅ IndexRightContent.vue (部分)
- ⏳ IndexBottomContent.vue
- ⏳ OverviewRightContent.vue
- ⏳ BaseRightContent.vue
- ⏳ BaseBottomContent.vue

## 🚀 下一步计划

1. 完成所有组件的数据迁移
2. 添加数据验证和错误处理
3. 实现数据持久化
4. 添加数据模拟和测试工具
5. 支持实时数据更新（WebSocket）

## 💡 最佳实践

1. **统一数据格式**：保持 JSON 数据结构的一致性
2. **合理分组**：按功能模块组织数据
3. **响应式使用**：在组件中使用 reactive 包装数据
4. **错误处理**：添加数据获取的错误处理逻辑
5. **性能优化**：大数据量时考虑分页和懒加载

## 🔍 调试技巧

```javascript
// 查看当前全局数据状态
console.log(dataService.globalData)

// 查看最后更新时间
console.log(dataService.getLastUpdateTime())

// 监听数据变化
watch(() => dataService.globalData, (newData) => {
  console.log('数据已更新:', newData)
}, { deep: true })
```
