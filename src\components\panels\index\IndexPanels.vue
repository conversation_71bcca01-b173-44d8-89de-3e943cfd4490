<template>
  <div class="index-panels">
    <IndexRightContent v-if="panelType === 'right'" />
    <IndexBottomContent v-if="panelType === 'bottom'" />
  </div>
</template>

<script setup>
import IndexRightContent from './IndexRightContent.vue'
import IndexBottomContent from './IndexBottomContent.vue'

const props = defineProps({
  panelType: {
    type: String,
    required: true,
    validator: (value) => ['right', 'bottom'].includes(value),
  },
})
</script>

<style lang="less" scoped>
.index-panels {
  height: 100%;
  padding: 10px;
  display: flex;
  flex-direction: column;
  min-height: 260px; // 确保最小高度
  overflow: visible; // 确保内容可见
}
</style>
