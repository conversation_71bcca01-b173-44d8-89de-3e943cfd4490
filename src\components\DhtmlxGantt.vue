<template>
  <div class="dhtmlx-gantt-container">
    <div ref="ganttContainer" class="gantt-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { gantt } from 'dhtmlx-gantt'
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css'

const props = defineProps({
  tasks: {
    type: Array,
    default: () => [],
  },
  links: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['task-click', 'task-dblclick', 'task-updated'])

const ganttContainer = ref(null)

// 配置甘特图
const configureGantt = () => {
  // 基础配置
  gantt.config.date_format = '%Y-%m-%d'
  gantt.config.scale_unit = 'day'
  gantt.config.date_scale = '%m/%d'
  gantt.config.subscales = [{ unit: 'month', step: 1, date: '%M %Y' }]

  // 锂电铜箔主题配色
  gantt.config.task_height = 20
  gantt.config.row_height = 40
  gantt.config.grid_width = 200
  gantt.config.grid_resize = true

  // 隐藏滚动条配置
  gantt.config.scroll_on_load = false
  gantt.config.show_grid = true
  gantt.config.show_chart = true

  // 列配置
  gantt.config.columns = [
    {
      name: 'text',
      label: '产线名称',
      width: 120,
      tree: true,
    },
    {
      name: 'start_date',
      label: '开始时间',
      width: 80,
      align: 'center',
    },
    {
      name: 'duration',
      label: '工期',
      width: 60,
      align: 'center',
    },
    {
      name: 'progress',
      label: '进度',
      width: 60,
      align: 'center',
      template: function (obj) {
        return Math.round(obj.progress * 100) + '%'
      },
    },
  ]

  // 自定义样式
  gantt.templates.task_class = function (start, end, task) {
    if (task.progress >= 1) {
      return 'gantt-task-completed'
    } else if (task.progress > 0) {
      return 'gantt-task-in-progress'
    } else {
      return 'gantt-task-not-started'
    }
  }

  // 任务文本模板 - 显示进度百分比在任务条上
  gantt.templates.task_text = function (start, end, task) {
    return Math.round(task.progress * 100) + '%'
  }

  // 进度条模板 - 隐藏进度条内的文字避免重复
  gantt.templates.progress_text = function (start, end, task) {
    return ''
  }

  // 工具提示
  gantt.templates.tooltip_text = function (start, end, task) {
    return `
      <b>任务:</b> ${task.text}<br/>
      <b>开始:</b> ${gantt.templates.tooltip_date_format(start)}<br/>
      <b>结束:</b> ${gantt.templates.tooltip_date_format(end)}<br/>
      <b>进度:</b> ${Math.round(task.progress * 100)}%
    `
  }
}

// 应用自定义样式
const applyCustomStyles = () => {
  const style = document.createElement('style')
  style.textContent = `
    .dhtmlx-gantt-container {
      width: 100%;
      height: 100%;
      background: transparent !important;
      border: none !important;
      border-radius: 0 !important;
      overflow: hidden;
      font-family: 'Microsoft YaHei', sans-serif;
    }

    /* 全局隐藏甘特图内所有滚动条 */
    .dhtmlx-gantt-container *::-webkit-scrollbar {
      width: 0px !important;
      height: 0px !important;
      display: none !important;
    }

    .dhtmlx-gantt-container * {
      scrollbar-width: none !important; /* Firefox */
      -ms-overflow-style: none !important; /* IE and Edge */
    }
    
    .gantt-container {
      width: 100%;
      height: 100%;
    }
    
    /* 甘特图主体样式 */
    .gantt_layout_root {
      background: transparent !important;
    }

    .gantt_grid_head_cell,
    .gantt_grid_head_add {
      background: transparent !important;
      color: #00d4ff !important;
      border-color: rgba(0, 212, 255, 0.3) !important;
      font-weight: 600 !important;
      font-size: 12px !important;
    }

    .gantt_grid_data .gantt_cell {
      color: #ffffff !important;
      border-color: rgba(0, 212, 255, 0.15) !important;
      background: transparent !important;
    }

    .gantt_grid_data .gantt_row:nth-child(odd) {
      background: transparent !important;
    }

    .gantt_grid_data .gantt_row:hover {
      background: transparent !important;
    }
    
    /* 时间轴样式 */
    .gantt_scale_line {
      background: transparent !important;
      color: #00d4ff !important;
      border-color: rgba(0, 212, 255, 0.3) !important;
      font-weight: 600 !important;
      font-size: 12px !important;
    }

    .gantt_task_scale .gantt_scale_cell {
      color: #75d1f0 !important;
      border-color: rgba(0, 212, 255, 0.15) !important;
      background: transparent !important;
    }
    
    /* 任务条样式 */
    .gantt_task_completed {
      background: transparent !important;
      border: 1px solid #00d4ff !important;
    }

    .gantt_task_in_progress {
      background: transparent !important;
      border: 1px solid #00ff88 !important;
    }

    .gantt_task_not_started {
      background: transparent !important;
      border: 1px solid rgba(116, 125, 140, 0.8) !important;
    }

    .gantt_task_progress {
      background: transparent !important;
    }

    .gantt_task_content {
      color: #ffffff !important;
      font-weight: 600 !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7) !important;
      font-size: 11px !important;
    }
    
    /* 网格线样式 */
    .gantt_task_bg {
      background: transparent !important;
      border-right: 1px solid rgba(0, 212, 255, 0.1) !important;
    }

    .gantt_task_bg:nth-child(odd) {
      background: transparent !important;
    }

    .gantt_task_bg:nth-child(7n) {
      background: transparent !important;
    }
    
    /* 隐藏所有滚动条但保持滚动功能 */
    .gantt_layout_content::-webkit-scrollbar,
    .gantt_task_vscroll::-webkit-scrollbar,
    .gantt_data_area::-webkit-scrollbar,
    .gantt_grid_data::-webkit-scrollbar,
    .gantt_task_area::-webkit-scrollbar,
    .gantt_layout_cell::-webkit-scrollbar,
    .gantt_grid::-webkit-scrollbar,
    .gantt_task::-webkit-scrollbar,
    .gantt_task_scale::-webkit-scrollbar,
    .gantt_grid_scale::-webkit-scrollbar,
    .gantt_layout_root::-webkit-scrollbar,
    .dhtmlx-gantt-container::-webkit-scrollbar,
    .gantt-container::-webkit-scrollbar {
      width: 0px !important;
      height: 0px !important;
      display: none !important;
    }

    .gantt_layout_content::-webkit-scrollbar-track,
    .gantt_task_vscroll::-webkit-scrollbar-track,
    .gantt_data_area::-webkit-scrollbar-track,
    .gantt_grid_data::-webkit-scrollbar-track,
    .gantt_task_area::-webkit-scrollbar-track,
    .gantt_layout_cell::-webkit-scrollbar-track,
    .gantt_grid::-webkit-scrollbar-track,
    .gantt_task::-webkit-scrollbar-track,
    .gantt_task_scale::-webkit-scrollbar-track,
    .gantt_grid_scale::-webkit-scrollbar-track,
    .gantt_layout_root::-webkit-scrollbar-track,
    .dhtmlx-gantt-container::-webkit-scrollbar-track,
    .gantt-container::-webkit-scrollbar-track {
      display: none !important;
    }

    .gantt_layout_content::-webkit-scrollbar-thumb,
    .gantt_task_vscroll::-webkit-scrollbar-thumb,
    .gantt_data_area::-webkit-scrollbar-thumb,
    .gantt_grid_data::-webkit-scrollbar-thumb,
    .gantt_task_area::-webkit-scrollbar-thumb,
    .gantt_layout_cell::-webkit-scrollbar-thumb,
    .gantt_grid::-webkit-scrollbar-thumb,
    .gantt_task::-webkit-scrollbar-thumb,
    .gantt_task_scale::-webkit-scrollbar-thumb,
    .gantt_grid_scale::-webkit-scrollbar-thumb,
    .gantt_layout_root::-webkit-scrollbar-thumb,
    .dhtmlx-gantt-container::-webkit-scrollbar-thumb,
    .gantt-container::-webkit-scrollbar-thumb {
      display: none !important;
    }

    /* 为Firefox和其他浏览器隐藏滚动条 */
    .gantt_layout_content,
    .gantt_task_vscroll,
    .gantt_data_area,
    .gantt_grid_data,
    .gantt_task_area,
    .gantt_layout_cell,
    .gantt_grid,
    .gantt_task,
    .gantt_task_scale,
    .gantt_grid_scale,
    .gantt_layout_root,
    .dhtmlx-gantt-container,
    .gantt-container {
      scrollbar-width: none !important; /* Firefox */
      -ms-overflow-style: none !important; /* IE and Edge */
    }
    
    /* 确保所有背景都透明 */
    .gantt_task_vscroll,
    .gantt_task_scaleP,
    .gantt_layout_cell,
    .gantt_grid,
    .gantt_task,
    .gantt_task_scale,
    .gantt_data_area,
    .gantt_grid_scale,
    .gantt_grid_data,
    .gantt_task_area {
      background: transparent !important;
    }

    /* 隐藏所有边框 */
    .gantt_grid_head_cell,
    .gantt_grid_data .gantt_cell,
    .gantt_scale_line,
    .gantt_task_scale .gantt_scale_cell {
      border: none !important;
    }

    .gantt_task_row, .gantt_row{
    background: transparent !important;
    }
    .gantt_task_line{
    background: #0CB8E6 !important;
    }
  `

  if (!document.querySelector('#dhtmlx-gantt-custom-styles')) {
    style.id = 'dhtmlx-gantt-custom-styles'
    document.head.appendChild(style)
  }
}

// 转换数据格式
const convertTasksData = (tasks) => {
  const ganttTasks = []
  let taskId = 1

  tasks.forEach((line) => {
    if (line.tasks && line.tasks.length > 0) {
      // 产线作为父任务
      const parentTask = {
        id: taskId++,
        text: line.name,
        start_date: line.start,
        duration: calculateDuration(line.start, line.end),
        progress: line.progress / 100,
        type: 'project',
        open: true,
      }
      ganttTasks.push(parentTask)

      // 子任务
      line.tasks.forEach((task) => {
        const childTask = {
          id: taskId++,
          text: task.name,
          start_date: task.start,
          duration: calculateDuration(task.start, task.end),
          progress: task.progress / 100,
          parent: parentTask.id,
        }
        ganttTasks.push(childTask)
      })
    } else {
      // 单个任务
      const task = {
        id: taskId++,
        text: line.name,
        start_date: line.start,
        duration: calculateDuration(line.start, line.end),
        progress: line.progress / 100,
      }
      ganttTasks.push(task)
    }
  })

  return ganttTasks
}

// 计算工期
const calculateDuration = (start, end) => {
  const startDate = new Date(start)
  const endDate = new Date(end)
  return Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1
}

// 初始化甘特图
const initGantt = () => {
  if (!ganttContainer.value) return

  configureGantt()
  applyCustomStyles()

  // 事件监听
  gantt.attachEvent('onTaskClick', function (id, e) {
    const task = gantt.getTask(id)
    emit('task-click', task)
    return true
  })

  gantt.attachEvent('onTaskDblClick', function (id, e) {
    const task = gantt.getTask(id)
    emit('task-dblclick', task)
    return true
  })

  gantt.attachEvent('onAfterTaskUpdate', function (id, task) {
    emit('task-updated', task)
  })

  // 初始化甘特图
  gantt.init(ganttContainer.value)

  // 加载数据
  loadData()
}

// 加载数据
const loadData = () => {
  const ganttData = {
    data: convertTasksData(props.tasks),
    links: props.links || [],
  }

  gantt.parse(ganttData)
}

// 监听数据变化
watch(
  () => props.tasks,
  () => {
    if (gantt.isInitialized) {
      gantt.clearAll()
      loadData()
    }
  },
  { deep: true },
)

onMounted(() => {
  initGantt()
})

onBeforeUnmount(() => {
  if (gantt.isInitialized) {
    gantt.destructor()
  }
})
</script>

<style scoped>
.dhtmlx-gantt-container {
  width: 100%;
  height: 100%;
}

.gantt-container {
  width: 100%;
  height: 100%;
}
</style>
