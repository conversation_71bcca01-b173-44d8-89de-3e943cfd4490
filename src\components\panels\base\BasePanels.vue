<template>
  <div class="base-panels">
    <BaseRightContent v-if="panelType === 'right'" />
    <BaseBottomContent v-if="panelType === 'bottom'" />
  </div>
</template>

<script setup>
import BaseRightContent from './BaseRightContent.vue'
import BaseBottomContent from './BaseBottomContent.vue'

const props = defineProps({
  panelType: {
    type: String,
    required: true,
    validator: (value) => ['right', 'bottom'].includes(value),
  },
})
</script>

<style lang="less" scoped>
.base-panels {
  height: 100%;
  padding: 10px;
}
</style>
