<template>
  <div class="production-panels">
    <ProductionRightContent v-if="panelType === 'right'" />
    <ProductionBottomContent v-if="panelType === 'bottom'" />
  </div>
</template>

<script setup>
import ProductionRightContent from './ProductionRightContent.vue'
import ProductionBottomContent from './ProductionBottomContent.vue'

const props = defineProps({
  panelType: {
    type: String,
    required: true,
    validator: (value) => ['right', 'bottom'].includes(value),
  },
})
</script>

<style lang="less" scoped>
.production-panels {
  height: 100%;
}
</style>
