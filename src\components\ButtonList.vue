<!--
decoFrameA2组件配置:
  scale:1 缩放等级 数字类型
  backgroundColor:"color" 背景颜色 颜色字符串类型
  borderColor:"color" 边框颜色 颜色字符串类型
  backgroundOpacity:1 背景透明的 数字类型
  decorationColor:["color","color"] 装饰元素颜色 颜色字符串类型
  textColor:"color" 文字颜色 颜色字符串类型
  glowColor:"color" 内发光颜色 颜色字符串类型
  glow:true 内发光 布尔类型
  border:true 边框开关 布尔类型
  directionAlt:false 方向切换 布尔类型
-->

<script setup>
// 定义 props
const props = defineProps({
  activeButtonId: {
    type: String,
    default: '',
  },
});

// 定义事件
const emit = defineEmits(['button-click']);

// 组件内部数据
const buttons = [
  { id: 'overview', label: '数据总览', icon: 'i carbon:dashboard' },
  { id: 'production', label: '生产管理', icon: 'i carbon:industry' },
  { id: 'energy', label: '能源管控', icon: 'i carbon:flash' },
  { id: 'emergency', label: '订单管理', icon: 'i carbon:order-details' },
];

// 使用计算属性来确保响应式更新
const buttonConfigs = computed(() => {
  return buttons.reduce((configs, button) => {
    const isActive = props.activeButtonId === button.id;
    configs[button.id] = {
      directionAlt: true,
      scale: 0.7,
      glow: isActive,
      glowColor: isActive ? '#00d4ff' : '',
      decorationColor: isActive
        ? ['#00d4ff', '#0099cc']
        : ['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.1)'],
      textColor: isActive ? '#ffffff' : 'rgba(255,255,255,0.7)',
    };
    return configs;
  }, {});
});

// 获取按钮的 decoFrame 配置
const getDecoFrameConfig = buttonId => {
  return buttonConfigs.value[buttonId];
};

// 处理按钮点击事件
const handleButtonClick = button => {
  emit('button-click', button);
};
</script>

<template>
  <div class="button-list">
    <div
      class="button-content"
      v-for="(button, index) in buttons"
      :key="button.id"
      :class="{
        disabled: button.disabled,
        active: props.activeButtonId === button.id,
      }"
      :style="{
        animationDelay: `${index * 0.1}s`,
      }"
      @click="handleButtonClick(button)"
    >
      <decoFrameA2
        :key="`${button.id}-${props.activeButtonId}`"
        :config="getDecoFrameConfig(button.id)"
      >
        <i :class="[button.icon, 'button-icon']"></i>
      </decoFrameA2>
      <span class="button-text">{{ button.label }}</span>
    </div>
  </div>
</template>

<style lang="less" scoped>
.button-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;

  .button-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    height: 70px;
    border-radius: 4px;

    // 入场动画
    opacity: 0;
    transform: translateX(-30px);
    animation: slideInFromLeft 0.6s ease-out forwards;

    .button-icon {
      font-size: 36px;
      opacity: 0.7;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
    }

    .button-text {
      flex: 1;
      margin-left: -10px;
      font-size: 20px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    // 激活状态
    &.active {
      .button-icon {
        opacity: 1;
        color: #00d4ff;
      }
      .button-text {
        color: #00d4ff;
      }
    }
  }
}

// 定义入场动画关键帧
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
