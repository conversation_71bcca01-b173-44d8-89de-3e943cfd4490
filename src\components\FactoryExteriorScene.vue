<template>
  <div ref="sceneContainer" class="factory-exterior-scene">
    <!-- 场景信息覆盖层 -->
    <div class="scene-overlay">
      <div class="scene-title">
        <h2>{{ selectedBase?.name || '工厂外景' }}</h2>
        <p>点击车间建筑直接进入车间内部</p>
      </div>

      <!-- 基地信息卡片 -->
      <div class="base-info-card" v-if="selectedBase">
        <div class="card-header">
          <i class="i carbon:building"></i>
          <span>{{ selectedBase.name }}</span>
        </div>
        <div class="card-content">
          <div class="info-row">
            <span class="label">产能：</span>
            <span class="value">{{ selectedBase.capacity }}</span>
          </div>
          <div class="info-row">
            <span class="label">状态：</span>
            <span class="value" :class="selectedBase.status">
              {{ selectedBase.status === 'running' ? '正常运行' : '维护中' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态覆盖层 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载工厂外景模型...</div>
        <div class="loading-progress">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: loadingProgress + '%' }"
            ></div>
          </div>
          <div class="progress-text">{{ loadingProgress }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

// 定义 props 和 emits
const props = defineProps({
  selectedBase: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['enter-workshop', 'back-to-map'])

// 场景容器引用
const sceneContainer = ref(null)

// 3D场景相关变量
let scene, camera, renderer, animationId, controls
let factoryModel = null

// 射线投射器和鼠标位置
let raycaster = new THREE.Raycaster()
let mouse = new THREE.Vector2()
let selectedObject = null
let hoveredObject = null // 当前悬停的对象

// 拖拽检测相关变量
let mouseDownPosition = { x: 0, y: 0 }
const dragThreshold = 5

// 存储原始材质的映射
const originalMaterials = new Map()

// 存储车间建筑对象，用于动画效果
let workshopBuildings = []

// 加载状态
const isLoading = ref(true)
const loadingProgress = ref(0)

// 获取对象详细信息
const getObjectDetails = (object) => {
  const details = {
    name: object.name || '未命名建筑',
    type: object.type,
    position: {
      x: object.position.x.toFixed(2),
      y: object.position.y.toFixed(2),
      z: object.position.z.toFixed(2),
    },
    rotation: {
      x: ((object.rotation.x * 180) / Math.PI).toFixed(1) + '°',
      y: ((object.rotation.y * 180) / Math.PI).toFixed(1) + '°',
      z: ((object.rotation.z * 180) / Math.PI).toFixed(1) + '°',
    },
  }

  // 根据对象名称添加特定信息
  if (
    object.name.includes('工厂') ||
    object.name.includes('车间') ||
    object.name.includes('厂房')
  ) {
    details.category = '生产建筑'
    details.function = '锂电铜箔生产车间'
    details.status = '可进入'
    details.area = '生产区域'
  } else if (object.name.includes('办公') || object.name.includes('管理')) {
    details.category = '办公建筑'
    details.function = '管理办公区域'
    details.status = '正常使用'
    details.area = '办公区域'
  } else if (object.name.includes('仓库') || object.name.includes('库房')) {
    details.category = '仓储建筑'
    details.function = '原料和成品仓储'
    details.status = '正常使用'
    details.area = '仓储区域'
  } else {
    details.category = '工厂建筑'
    details.function = '工厂设施'
    details.status = '正常'
    details.area = '厂区'
  }

  return details
}

// 鼠标事件处理
const onMouseDown = (event) => {
  mouseDownPosition.x = event.clientX
  mouseDownPosition.y = event.clientY
}

const onMouseUp = (event) => {
  const deltaX = Math.abs(event.clientX - mouseDownPosition.x)
  const deltaY = Math.abs(event.clientY - mouseDownPosition.y)
  const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

  if (distance < dragThreshold) {
    if (event.button === 0) {
      onMouseClick(event)
    } else if (event.button === 2) {
      onRightClick(event)
    }
  }
}

const onMouseClick = (event) => {
  if (!camera || !scene) return

  const rect = renderer.domElement.getBoundingClientRect()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

  raycaster.setFromCamera(mouse, camera)
  const intersects = raycaster.intersectObjects(scene.children, true)

  if (intersects.length > 0) {
    const clickedObject = intersects[0].object

    // 检查是否点击的是车间建筑，如果是则直接进入车间
    if (isWorkshopBuilding(clickedObject)) {
      console.log('点击车间建筑，直接进入车间:', clickedObject.name)
      enterWorkshop(clickedObject)
      return
    }

    // 对于非车间建筑，只进行高亮处理
    if (selectedObject === clickedObject) {
      highlightObject(selectedObject, false)
      selectedObject = null
    } else {
      if (selectedObject) {
        highlightObject(selectedObject, false)
      }

      selectedObject = clickedObject
      highlightObject(clickedObject, true)
      console.log('选中工厂建筑:', clickedObject.name)
    }
  } else {
    if (selectedObject) {
      highlightObject(selectedObject, false)
      selectedObject = null
    }
  }
}

// 判断是否为车间建筑
const isWorkshopBuilding = (object) => {
  if (!object || !object.name) return false

  const workshopKeywords = [
    '车间',
    '工厂',
    '厂房',
    '生产',
    'workshop',
    'factory',
    'production',
  ]
  const objectName = object.name.toLowerCase()

  return workshopKeywords.some(
    (keyword) =>
      objectName.includes(keyword) ||
      objectName.includes(keyword.toLowerCase()),
  )
}

// 为车间建筑应用蓝色透明材质
const applyWorkshopMaterial = (meshObject) => {
  if (!meshObject || !meshObject.material) return

  // 保存原始材质
  if (!originalMaterials.has(meshObject.uuid + '_original')) {
    originalMaterials.set(
      meshObject.uuid + '_original',
      meshObject.material.clone(),
    )
  }

  // 创建蓝色透明材质
  const workshopMaterial = new THREE.MeshLambertMaterial({
    color: 0x00d4ff, // 蓝色
    transparent: true, // 启用透明
    opacity: 0.3, // 透明度30%，更加透明
    emissive: 0x003366, // 轻微的蓝色发光
    emissiveIntensity: 0.3, // 发光强度
  })

  // 应用材质
  if (Array.isArray(meshObject.material)) {
    meshObject.material = meshObject.material.map(() => workshopMaterial)
  } else {
    meshObject.material = workshopMaterial
  }

  // 将车间建筑添加到数组中，用于动画效果
  if (!workshopBuildings.includes(meshObject)) {
    workshopBuildings.push(meshObject)
  }

  console.log('为车间建筑应用蓝色透明材质:', meshObject.name)
}

// 为车间建筑应用悬停高亮材质
const applyHoverMaterial = (meshObject) => {
  if (!meshObject || !meshObject.material) return

  // 创建悬停时的高亮材质
  const hoverMaterial = new THREE.MeshLambertMaterial({
    color: 0x00ffff, // 更亮的青色
    transparent: true, // 启用透明
    opacity: 0.8, // 更高的透明度
    emissive: 0x0066cc, // 更强的蓝色发光
    emissiveIntensity: 0.6, // 更高的发光强度
  })

  // 应用悬停材质
  if (Array.isArray(meshObject.material)) {
    meshObject.material = meshObject.material.map(() => hoverMaterial)
  } else {
    meshObject.material = hoverMaterial
  }
}

// 恢复车间建筑的默认蓝色透明材质
const restoreWorkshopMaterial = (meshObject) => {
  if (!meshObject || !meshObject.material) return

  // 恢复默认的车间材质
  const workshopMaterial = new THREE.MeshLambertMaterial({
    color: 0x00d4ff, // 蓝色
    transparent: true, // 启用透明
    opacity: 0.3, // 透明度30%，更加透明
    emissive: 0x003366, // 轻微的蓝色发光
    emissiveIntensity: 0.3, // 发光强度
  })

  // 应用默认材质
  if (Array.isArray(meshObject.material)) {
    meshObject.material = meshObject.material.map(() => workshopMaterial)
  } else {
    meshObject.material = workshopMaterial
  }
}

// 鼠标移动事件处理（悬停效果）
const onMouseMove = (event) => {
  if (!camera || !scene) return

  const rect = renderer.domElement.getBoundingClientRect()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

  raycaster.setFromCamera(mouse, camera)
  const intersects = raycaster.intersectObjects(scene.children, true)

  if (intersects.length > 0) {
    const hoveredObj = intersects[0].object

    // 只对车间建筑进行悬停处理
    if (isWorkshopBuilding(hoveredObj)) {
      // 如果悬停的对象发生变化
      if (hoveredObject !== hoveredObj) {
        // 恢复之前悬停对象的样式
        if (hoveredObject && hoveredObject !== selectedObject) {
          restoreWorkshopMaterial(hoveredObject)
        }

        // 高亮新的悬停对象
        hoveredObject = hoveredObj
        if (hoveredObject !== selectedObject) {
          applyHoverMaterial(hoveredObject)
        }

        // 改变鼠标样式
        renderer.domElement.style.cursor = 'pointer'
      }
    } else {
      // 悬停到非车间建筑，清除悬停效果
      if (hoveredObject && hoveredObject !== selectedObject) {
        restoreWorkshopMaterial(hoveredObject)
      }
      hoveredObject = null
      renderer.domElement.style.cursor = 'default'
    }
  } else {
    // 没有悬停对象
    if (hoveredObject && hoveredObject !== selectedObject) {
      restoreWorkshopMaterial(hoveredObject)
    }
    hoveredObject = null
    renderer.domElement.style.cursor = 'default'
  }
}

const onRightClick = (event) => {
  event.preventDefault()

  if (selectedObject) {
    highlightObject(selectedObject, false)
    selectedObject = null
  }

  if (hoveredObject) {
    restoreWorkshopMaterial(hoveredObject)
    hoveredObject = null
  }
}

// 高亮对象
const highlightObject = (object, highlight) => {
  if (!object || !object.material) return

  if (highlight) {
    if (!originalMaterials.has(object.uuid + '_highlight')) {
      originalMaterials.set(object.uuid + '_highlight', object.material.clone())
    }

    if (Array.isArray(object.material)) {
      object.material.forEach((material) => {
        material.emissive.setHex(0x00d4ff)
        material.needsUpdate = true
      })
    } else {
      object.material.emissive.setHex(0x00d4ff)
      object.material.needsUpdate = true
    }
  } else {
    if (Array.isArray(object.material)) {
      object.material.forEach((material) => {
        material.emissive.setHex(0x000000)
        material.needsUpdate = true
      })
    } else {
      object.material.emissive.setHex(0x000000)
      object.material.needsUpdate = true
    }
  }
}

// 进入车间
const enterWorkshop = (buildingObject = null) => {
  const targetObject = buildingObject || selectedObject
  if (targetObject) {
    emit('enter-workshop', {
      buildingName: targetObject.name,
      baseInfo: props.selectedBase,
    })
  }
}

// 初始化3D场景
const initScene = () => {
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x87ceeb) // 天空蓝色背景

  const containerWidth = sceneContainer.value.clientWidth
  const containerHeight = sceneContainer.value.clientHeight

  camera = new THREE.PerspectiveCamera(
    75,
    containerWidth / containerHeight,
    0.1,
    1000,
  )
  camera.position.set(50, 30, 50)

  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(containerWidth, containerHeight)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap

  if (sceneContainer.value) {
    sceneContainer.value.appendChild(renderer.domElement)
  }

  // 添加事件监听器
  renderer.domElement.addEventListener('mousedown', onMouseDown)
  renderer.domElement.addEventListener('mouseup', onMouseUp)
  renderer.domElement.addEventListener('mousemove', onMouseMove)
  renderer.domElement.addEventListener('contextmenu', (e) => e.preventDefault())

  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = false
  controls.maxPolarAngle = Math.PI / 2

  // 添加光源
  // 环境光 - 提供基础照明
  const ambientLight = new THREE.AmbientLight(0x404040, 1.2)
  scene.add(ambientLight)

  // 主方向光 - 模拟太阳光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 6)
  directionalLight.position.set(50, 50, 50)
  directionalLight.castShadow = true
  directionalLight.shadow.mapSize.width = 2048
  directionalLight.shadow.mapSize.height = 2048
  directionalLight.shadow.camera.near = 0.1
  directionalLight.shadow.camera.far = 200
  directionalLight.shadow.camera.left = -100
  directionalLight.shadow.camera.right = 100
  directionalLight.shadow.camera.top = 100
  directionalLight.shadow.camera.bottom = -100
  scene.add(directionalLight)

  // 辅助方向光 - 从左侧照射
  const directionalLight2 = new THREE.DirectionalLight(0xffffff, 3)
  directionalLight2.position.set(-40, 30, 20)
  scene.add(directionalLight2)

  // 辅助方向光 - 从右后方照射
  const directionalLight3 = new THREE.DirectionalLight(0xffffff, 2.5)
  directionalLight3.position.set(30, 25, -40)
  scene.add(directionalLight3)

  // 点光源 - 工厂内部照明效果
  const pointLight1 = new THREE.PointLight(0x00d4ff, 2, 80)
  pointLight1.position.set(0, 20, 0)
  scene.add(pointLight1)

  // 点光源 - 工厂前方照明
  const pointLight2 = new THREE.PointLight(0xffffff, 1.5, 60)
  pointLight2.position.set(20, 15, 30)
  scene.add(pointLight2)

  // 点光源 - 工厂后方照明
  const pointLight3 = new THREE.PointLight(0xffffff, 1.5, 60)
  pointLight3.position.set(-20, 15, -30)
  scene.add(pointLight3)

  // 半球光 - 模拟天空光照
  const hemisphereLight = new THREE.HemisphereLight(0x87ceeb, 0x654321, 0.8)
  scene.add(hemisphereLight)

  // 加载工厂外景模型
  loadFactoryModel()
}

// 加载工厂外景模型
const loadFactoryModel = () => {
  const loader = new GLTFLoader()

  // 设置初始加载状态
  isLoading.value = true
  loadingProgress.value = 0

  loader.load(
    '/model/工厂3.glb',
    (gltf) => {
      factoryModel = gltf.scene
      factoryModel.scale.setScalar(1)
      factoryModel.position.set(0, 0, 0)

      // 为模型添加阴影和车间材质
      factoryModel.traverse((child) => {
        if (child.isMesh) {
          child.castShadow = true
          child.receiveShadow = true

          // 检查是否为车间建筑，如果是则应用蓝色透明材质
          if (isWorkshopBuilding(child)) {
            applyWorkshopMaterial(child)
          }
        }
      })

      scene.add(factoryModel)

      // 模型加载完成，隐藏加载状态
      loadingProgress.value = 100
      setTimeout(() => {
        isLoading.value = false
      }, 300) // 短暂延迟让用户看到100%

      console.log('工厂外景模型加载完成')
    },
    (progress) => {
      // 更新加载进度
      if (progress.lengthComputable) {
        const percentComplete = (progress.loaded / progress.total) * 100
        loadingProgress.value = Math.round(percentComplete)
        console.log('工厂外景模型加载进度:', percentComplete + '%')
      }
    },
    (error) => {
      console.error('工厂外景模型加载失败:', error)
      isLoading.value = false
    },
  )
}

// 动画循环
const animate = () => {
  animationId = requestAnimationFrame(animate)

  // 为车间建筑添加闪烁效果
  const time = Date.now() * 0.002
  workshopBuildings.forEach((building) => {
    if (building && building.material) {
      // 使用正弦波创建闪烁效果
      const intensity = 0.3 + Math.sin(time * 2) * 0.2
      if (Array.isArray(building.material)) {
        building.material.forEach((material) => {
          if (material.emissiveIntensity !== undefined) {
            material.emissiveIntensity = intensity
          }
        })
      } else {
        if (building.material.emissiveIntensity !== undefined) {
          building.material.emissiveIntensity = intensity
        }
      }
    }
  })

  if (renderer && scene && camera) {
    renderer.render(scene, camera)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (!camera || !renderer || !sceneContainer.value) return

  const containerWidth = sceneContainer.value.clientWidth
  const containerHeight = sceneContainer.value.clientHeight

  camera.aspect = containerWidth / containerHeight
  camera.updateProjectionMatrix()
  renderer.setSize(containerWidth, containerHeight)
}

// 清理资源
const cleanup = () => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }

  if (controls) {
    controls.dispose()
  }

  if (renderer) {
    renderer.domElement.removeEventListener('mousedown', onMouseDown)
    renderer.domElement.removeEventListener('mouseup', onMouseUp)
    renderer.domElement.removeEventListener('mousemove', onMouseMove)
    renderer.domElement.removeEventListener('contextmenu', (e) =>
      e.preventDefault(),
    )
    renderer.dispose()
  }

  selectedObject = null
  hoveredObject = null
  workshopBuildings = []
  originalMaterials.clear()
}

onMounted(() => {
  initScene()
  animate()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  cleanup()
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.factory-exterior-scene {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #87ceeb, #98d8e8);
  border-radius: 12px;
  overflow: hidden;

  .scene-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;

    .scene-title {
      position: absolute;
      top: 20px;
      left: 20px;
      color: white;

      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #00d4ff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      }

      p {
        margin: 8px 0 0 0;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      }
    }

    .base-info-card {
      position: absolute;
      top: 20px;
      right: 20px;
      background: linear-gradient(
        145deg,
        rgba(0, 212, 255, 0.15),
        rgba(0, 153, 204, 0.15)
      );
      border: 1px solid rgba(0, 212, 255, 0.3);
      border-radius: 12px;
      padding: 16px;
      min-width: 250px;
      backdrop-filter: blur(10px);
      box-shadow: 0 8px 32px rgba(0, 212, 255, 0.2);
      pointer-events: none;

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        color: #00d4ff;
        font-size: 16px;
        font-weight: 600;

        i {
          font-size: 18px;
        }
      }

      .card-content {
        .info-row {
          display: flex;
          margin-bottom: 6px;
          font-size: 13px;

          .label {
            color: rgba(255, 255, 255, 0.8);
            min-width: 50px;
          }

          .value {
            color: white;
            font-weight: 600;

            &.running {
              color: #4caf50;
            }

            &.maintenance {
              color: #ffae00;
            }
          }
        }
      }
    }
  }
  /* 加载状态覆盖层 */
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
  }

  .loading-content {
    text-align: center;
    color: white;
    max-width: 300px;
  }

  .loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(0, 212, 255, 0.3);
    border-top: 4px solid #00d4ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  .loading-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #00d4ff;
  }

  .loading-progress {
    margin-top: 15px;
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #0099cc);
    border-radius: 3px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  }

  .progress-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
