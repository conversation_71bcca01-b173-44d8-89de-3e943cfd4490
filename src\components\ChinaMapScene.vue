<template>
  <div class="china-map-scene">
    <div class="map-container">
      <echartsInit
        :chartOption="mapOption"
        @chart-click="handleMapClick"
        ref="mapRef"
      />
    </div>

    <!-- 地图标题和信息 -->
    <div class="map-overlay">
      <div class="map-title">
        <h2>锂电铜箔生产基地分布</h2>
        <p>点击基地图标进入3D工厂场景</p>
      </div>

      <!-- 基地信息卡片 -->
      <div class="base-info-cards" v-if="selectedBase">
        <div class="info-card">
          <div class="card-header">
            <i class="i carbon:location"></i>
            <span>{{ selectedBase.name }}</span>
          </div>
          <div class="card-content">
            <div class="info-item">
              <span class="label">产能：</span>
              <span class="value">{{ selectedBase.capacity }}</span>
            </div>
            <div class="info-item">
              <span class="label">员工：</span>
              <span class="value">{{ selectedBase.employees }}人</span>
            </div>
            <div class="info-item">
              <span class="label">状态：</span>
              <span class="value" :class="selectedBase.status">
                {{ selectedBase.status === 'running' ? '正常运行' : '维护中' }}
              </span>
            </div>
            <div class="info-item description">
              <span class="label">描述：</span>
              <span class="value">{{ selectedBase.description }}</span>
            </div>
          </div>
          <div class="card-actions">
            <button class="enter-3d-btn" @click="enter3DScene">
              <i class="i carbon:3d-mpr-toggle"></i>
              进入3D工厂
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import chinaJson from '@/utils/map/中华人民共和国.json'

// 定义事件
const emit = defineEmits(['enter-3d-scene'])

// 地图引用
const mapRef = ref(null)

// 选中的基地
const selectedBase = ref(null)

// 基地数据
const baseData = [
  {
    name: '华东基地',
    value: [121.4737, 31.2304, 3500],
    capacity: '3500吨/月',
    status: 'running',
    employees: 280,
    description: '主要生产高精度锂电铜箔，配备最新的电解设备',
    id: 'huadong',
  },
  {
    name: '华南基地',
    value: [113.2644, 23.1291, 2800],
    capacity: '2800吨/月',
    status: 'running',
    employees: 220,
    description: '专注新能源汽车用铜箔，技术领先',
    id: 'huanan',
  },
  {
    name: '西南基地',
    value: [104.0665, 30.5728, 2200],
    capacity: '2200吨/月',
    status: 'running',
    employees: 180,
    description: '储能系统铜箔生产基地，技术先进',
    id: 'xinan',
  },
]

// 地图配置
const mapOption = reactive({})

// 处理地图点击事件
const handleMapClick = (params) => {
  console.log('地图点击事件：', params)
  if (params.componentType === 'series' && params.seriesType === 'scatter') {
    const clickedBase = baseData.find((base) => base.name === params.data.name)
    if (clickedBase) {
      selectedBase.value = clickedBase
      console.log('选中基地:', clickedBase)
    }
  } else {
    // 点击空白区域，取消选择
    selectedBase.value = null
  }
}

// 进入3D场景
const enter3DScene = () => {
  if (selectedBase.value) {
    emit('enter-3d-scene', selectedBase.value)
  }
}

// 初始化地图配置
const initMapOption = () => {
  // 注册中国地图
  echarts.registerMap('china', chinaJson)

  Object.assign(mapOption, {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if (
          params.componentType === 'series' &&
          params.seriesType === 'scatter'
        ) {
          const data = params.data
          const baseInfo = baseData.find((base) => base.name === data.name)
          if (baseInfo) {
            return `
              <div style="padding: 16px; min-width: 280px; max-width: 320px;">
                <div style="font-weight: bold; color: #00d4ff; margin-bottom: 12px; font-size: 16px;">
                  ${baseInfo.name}
                </div>
                <div style="color: #ffffff; font-size: 13px; line-height: 1.8;">
                  <div><i class="i carbon:industry" style="color: #00d4ff; margin-right: 8px; font-size: 14px;"></i>产能：${
                    baseInfo.capacity
                  }</div>
                  <div><i class="i carbon:user-multiple" style="color: #00d4ff; margin-right: 8px; font-size: 14px;"></i>员工：${
                    baseInfo.employees
                  }人</div>
                  <div><i class="i carbon:circle-solid" style="color: #00d4ff; margin-right: 8px; font-size: 14px;"></i>状态：${
                    baseInfo.status === 'running'
                      ? '<span style="color: #00ff88;">正常运行</span>'
                      : '<span style="color: #ffae00;">维护中</span>'
                  }</div>
                  <div style="margin-top: 10px; color: #99ccff; font-style: italic; font-size: 12px;">
                    ${baseInfo.description}
                  </div>
                  <div style="margin-top: 12px; color: #00d4ff; font-weight: bold; font-size: 13px;">
                    <i class="i carbon:cube" style="margin-right: 8px; font-size: 14px;"></i>点击进入3D工厂场景
                  </div>
                </div>
              </div>
            `
          }
        }
        return params.name
      },
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff' },
    },
    geo: {
      map: 'china',
      roam: true,
      zoom: 1.2,
      center: [104, 35],
      scaleLimit: {
        min: 0.8,
        max: 3,
      },
      label: {
        show: false,
        color: 'rgba(0, 212, 255, 0.6)',
        fontSize: 12,
      },
      itemStyle: {
        areaColor: 'rgba(0, 59, 92, 0.2)',
        borderColor: 'rgba(0, 212, 255, 0.3)',
        borderWidth: 1,
      },
      emphasis: {
        label: {
          show: false,
        },
        itemStyle: {
          areaColor: 'rgba(0, 91, 139, 0.4)',
          borderColor: '#00d4ff',
          borderWidth: 2,
        },
      },
    },
    series: [
      {
        name: '基地分布',
        type: 'scatter',
        coordinateSystem: 'geo',
        symbol: 'pin',
        symbolSize: (val) => {
          const capacity = val[2]
          return Math.max(35, Math.min(60, capacity / 80 + 25))
        },
        data: baseData.map((base) => ({
          name: base.name,
          value: base.value,
          itemStyle: {
            color: base.status === 'running' ? '#00d4ff' : '#ffae00',
            shadowBlur: 12,
            shadowColor: base.status === 'running' ? '#00d4ff' : '#ffae00',
          },
        })),
        label: {
          show: true,
          position: 'bottom',
          color: '#00d4ff',
          fontSize: 13,
          fontWeight: 'bold',
          formatter: '{b}',
          offset: [0, 8],
        },
        emphasis: {
          scale: 1.3,
          label: {
            show: true,
            color: '#ffffff',
            fontSize: 14,
          },
        },
      },
    ],
  })
}

onMounted(() => {
  initMapOption()
})
</script>

<style lang="less" scoped>
.china-map-scene {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 20, 40, 0.8) 0%,
    rgba(0, 40, 80, 0.6) 50%,
    rgba(0, 20, 40, 0.8) 100%
  );
  border-radius: 12px;
  overflow: hidden;

  .map-container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;

    .map-title {
      position: absolute;
      top: 20px;
      left: 20px;
      color: white;
      pointer-events: none;

      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #00d4ff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      }

      p {
        margin: 8px 0 0 0;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      }
    }

    .base-info-cards {
      position: absolute;
      top: 20px;
      right: 20px;
      pointer-events: auto;

      .info-card {
        background: linear-gradient(
          145deg,
          rgba(0, 212, 255, 0.15),
          rgba(0, 153, 204, 0.15)
        );
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 12px;
        padding: 16px;
        min-width: 280px;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 212, 255, 0.2);
        animation: slideInRight 0.3s ease-out;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          color: #00d4ff;
          font-size: 16px;
          font-weight: 600;

          i {
            font-size: 18px;
          }
        }

        .card-content {
          .info-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 13px;

            &.description {
              flex-direction: column;
              gap: 4px;
            }

            .label {
              color: rgba(255, 255, 255, 0.8);
              min-width: 50px;
              font-weight: 500;
            }

            .value {
              color: white;
              font-weight: 600;

              &.running {
                color: #4caf50;
              }

              &.maintenance {
                color: #ffae00;
              }
            }
          }
        }

        .card-actions {
          margin-top: 16px;
          padding-top: 12px;
          border-top: 1px solid rgba(0, 212, 255, 0.2);

          .enter-3d-btn {
            width: 100%;
            padding: 10px 16px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;

            &:hover {
              background: linear-gradient(135deg, #00b8e6, #0088bb);
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 212, 255, 0.4);
            }

            &:active {
              transform: translateY(0);
            }

            i {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
