<template>
  <div class="bottom-content">
    <!-- 左侧生产数据表格 -->
    <div class="left-section">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:data-table icon"></i>
        </decoFrameB2>
        <span class="title">生产数据监控</span>
      </div>
      <div class="chart-wrapper">
        <echartsInit :chartOption="productionChartOption" />
      </div>
    </div>

    <!-- 右侧成品入库柱状图 -->
    <div class="right-section">
      <div class="data-title">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:chart-bar icon"></i>
        </decoFrameB2>
        <span class="title">成品入库情况</span>
      </div>
      <div class="bar-wrapper">
        <echartsInit :chartOption="inventoryOption" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

const decoFrameConfig = {
  directionAlt: true,
  scale: 0.8,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 40,
}

// 生产数据表格数据 - 使用数据服务
const productionData = reactive(dataService.getProductionTableData())

// 生产数据折线图配置
const productionChartOption = ref({})

// 成品入库柱状图
const inventoryOption = ref({})

// 处理生产数据折线图配置
const processProductionChartOption = () => {
  // 提取周次作为X轴数据
  const weeks = productionData.map((item) => `第${item.week}周`)

  // 提取各项数据
  const plannedProduction = productionData.map((item) => item.plannedProduction)
  const predictedDemand = productionData.map((item) => item.predictedDemand)
  const actualDemand = productionData.map((item) => item.actualDemand)
  const inventory = productionData.map((item) => item.inventory)
  const serviceLevel = productionData.map((item) =>
    (item.serviceLevel * 100).toFixed(1),
  )

  productionChartOption.value = {
    update: false,
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      textStyle: { color: '#ffffff' },
    },
    legend: {
      data: ['生产计划', '预测需求', '实际需求', '库存量', '服务水平(%)'],
      textStyle: { color: '#ffffff', fontSize: 10 },
      top: '2%',
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 8,
    },
    grid: {
      left: '2%',
      right: '2%',
      top: '20%',
      bottom: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: weeks,
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: {
        color: '#aaddff',
        fontSize: 9,
        rotate: 0,
        margin: 6,
      },
      axisTick: { show: false },
    },
    yAxis: [
      {
        type: 'value',
        name: '数量(件)',
        nameTextStyle: {
          color: '#aaddff',
          fontSize: 9,
          padding: [0, 0, 0, 0],
        },
        axisLine: { show: false },
        axisLabel: {
          color: '#aaddff',
          fontSize: 9,
          margin: 4,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(0,212,255,0.1)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        name: '服务水平(%)',
        nameTextStyle: {
          color: '#aaddff',
          fontSize: 9,
          padding: [0, 0, 0, 0],
        },
        position: 'right',
        axisLine: { show: false },
        axisLabel: {
          color: '#aaddff',
          fontSize: 9,
          margin: 4,
        },
        splitLine: { show: false },
        min: 90,
        max: 100,
      },
    ],
    series: [
      {
        name: '生产计划',
        type: 'line',
        data: plannedProduction,
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#00d4ff', width: 2 },
        itemStyle: { color: '#00d4ff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0,212,255,0.3)' },
              { offset: 1, color: 'rgba(0,212,255,0)' },
            ],
          },
        },
      },
      {
        name: '预测需求',
        type: 'line',
        data: predictedDemand,
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#66ccff', width: 2 },
        itemStyle: { color: '#66ccff' },
      },
      {
        name: '实际需求',
        type: 'line',
        data: actualDemand,
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#ff6b6b', width: 2 },
        itemStyle: { color: '#ff6b6b' },
      },
      {
        name: '库存量',
        type: 'line',
        data: inventory,
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#4ecdc4', width: 2 },
        itemStyle: { color: '#4ecdc4' },
      },
      {
        name: '服务水平(%)',
        type: 'line',
        yAxisIndex: 1,
        data: serviceLevel,
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#ffd93d', width: 3 },
        itemStyle: { color: '#ffd93d' },
      },
    ],
  }
}

const processInventoryOption = () => {
  inventoryOption.value = {
    update: false,
    tooltip: { trigger: 'axis' },
    legend: {
      data: ['产品', '副产品'],
      textStyle: { color: '#ffffff' },
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '6%',
      bottom: '0%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [
        '08/01',
        '08/02',
        '08/03',
        '08/04',
        '08/05',
        '08/06',
        '08/07',
        '08/08',
      ],
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#aaddff', fontSize: 11 },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisLabel: { color: '#aaddff' },
      splitLine: {
        lineStyle: { color: 'rgba(0,212,255,0.1)', type: 'dashed' },
      },
    },
    series: [
      {
        name: '产品',
        type: 'bar',
        data: [320, 280, 350, 430, 380, 290, 310, 500],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#00d4ff' },
              { offset: 1, color: 'rgba(0,212,255,0)' },
            ],
          },
        },
      },
      {
        name: '副产品',
        type: 'bar',
        data: [120, 150, 180, 200, 160, 140, 170, 220],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#66ccff' },
              { offset: 1, color: 'rgba(102,204,255,0)' },
            ],
          },
        },
      },
    ],
  }
}

onMounted(() => {
  processProductionChartOption()
  processInventoryOption()
})
</script>

<style lang="less" scoped>
.bottom-content {
  display: flex;
  gap: 12px;
  height: 100%;

  .data-title {
    display: flex;
    align-items: center;
    height: 40px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.15),
      rgba(0, 212, 255, 0.05)
    );
    backdrop-filter: blur(10px);

    .icon {
      font-size: 32px;
      color: #00d4ff;
    }

    .title {
      color: #75d1f0;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .left-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .chart-wrapper {
      flex: 1;
      height: 100%;
      position: relative;
      background: radial-gradient(
        circle at center,
        rgba(0, 212, 255, 0.08) 0%,
        rgba(0, 212, 255, 0.02) 70%
      );
      border: 1px solid rgba(0, 212, 255, 0.15);
      border-radius: 12px;
      backdrop-filter: blur(6px);

      // 确保图表组件撑满容器
      :deep(.echarts-container) {
        width: 100% !important;
        height: 100% !important;
      }

      :deep(canvas) {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }

  .right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .bar-wrapper {
      flex: 1;
      height: 100%;
      position: relative;
      background: radial-gradient(
        circle at center,
        rgba(0, 212, 255, 0.12) 0%,
        rgba(0, 212, 255, 0.02) 70%
      );
      border: 1px solid rgba(0, 212, 255, 0.15);
      border-radius: 12px;
      backdrop-filter: blur(6px);
    }
  }
}
</style>
