<template>
  <div class="right-content">
    <!-- 当日实时生产量 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:task-view icon"></i>
        </decoFrameB2>
        <span class="title">当日实时生产量</span>
      </div>
      <div class="data-card-body">
        <div class="production-type-grid">
          <div
            class="type-item"
            v-for="(item, index) in prodState.typeData"
            :key="index"
          >
            <div class="circle">
              <i :class="['i', item.icon]"></i>
            </div>
            <span class="type-name">{{ item.name }}</span>
            <div class="type-value-wrapper">
              <DigitalTransform
                class="type-value"
                :value="item.value"
                :useGrouping="true"
                :interval="1200"
              />
              <span class="unit">吨</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:inventory-management icon"></i>
        </decoFrameB2>
        <span class="title">铜箔库存</span>
      </div>
      <div class="data-card-body">
        <div class="inventory-grid">
          <div
            class="inv-item"
            v-for="(item, idx) in storageState.inventory"
            :key="idx"
          >
            <div class="name">{{ item.name }}</div>
            <div class="value-wrap">
              <DigitalTransform
                class="value"
                :value="item.value"
                :useGrouping="true"
              />
              <span class="unit">吨</span>
            </div>
            <div class="progress">
              <div
                class="bar"
                :style="{
                  width: ((item.value / item.capacity) * 100).toFixed(0) + '%',
                }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 累计生产时长 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:time icon"></i>
        </decoFrameB2>
        <span class="title">累计生产时长</span>
        <div class="total-wrap">
          <DigitalTransform class="total-value" :value="timeState.total" />
          <span class="unit">小时</span>
        </div>
      </div>
      <div class="data-card-body time-body">
        <div class="pie-container">
          <echartsInit :chartOption="timeState.pieOption" />
          <div class="pill-list">
            <div
              class="pill"
              v-for="(seg, idx) in timeState.segments"
              :key="idx"
              :class="seg.key"
            >
              <span class="label">{{ seg.name }}</span>
              <DigitalTransform class="val" :value="seg.value" />
              <span class="u">h</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 周生产时长柱状图 -->
    <div class="data-card">
      <div class="data-header">
        <decoFrameB2 :config="decoFrameConfig">
          <i class="i carbon:chart-column icon"></i>
        </decoFrameB2>
        <span class="title">周生产时长</span>
      </div>
      <div class="data-card-body">
        <div class="bar-container">
          <echartsInit :chartOption="weekBarState.chartOption" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import { useDataService } from '@/composables/useDataService'

// 使用数据服务
const dataService = useDataService()

// decoFrameB2 基础配置：统一为蓝青色 (#00d4ff) 主题，与 Overview 保持一致
const decoFrameConfig = {
  directionAlt: true,
  scale: 0.8,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 40,
}

/* ======================= 实时生产量 - 使用数据服务 ======================= */
const prodState = reactive({
  typeData: dataService.getProductionRightData().realTimeProduction,
})

/* ======================= 铜箔库存 - 使用数据服务 ======================= */
const storageState = reactive({
  inventory: dataService.getProductionRightData().inventory,
})

/* ======================= 生产时长 - 使用数据服务 ======================= */
const timeState = reactive({
  segments: dataService.getProductionRightData().productionTime,
  total: 0,
  pieOption: {},
})

const calcTotal = () => {
  timeState.total = timeState.segments.reduce((sum, s) => sum + s.value, 0)
}

const processPieOption = () => {
  timeState.pieOption = {
    update: false,
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}h ({d}%)',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { color: '#ffffff', fontSize: 12 },
    },
    legend: { show: false },
    series: [
      {
        type: 'pie',
        roseType: 'area',
        radius: ['25%', '85%'],
        center: ['50%', '50%'],
        itemStyle: { borderRadius: 6 },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c}h',
          color: '#ffffff',
          fontSize: 16,
        },
        labelLine: {
          show: true,
          length: 18,
          length2: 12,
          lineStyle: { color: '#00d4ff' },
        },
        data: timeState.segments.map((seg) => ({
          value: seg.value,
          name: seg.name,
          itemStyle: { color: seg.color },
        })),
      },
    ],
  }
}

calcTotal()
processPieOption()

/* ======================= 周生产时长柱状图 ======================= */
const weekBarState = reactive({
  chartOption: {},
})

const processWeekBarOption = () => {
  // 使用数据服务获取周生产时长数据
  const weeklyData = dataService.getWeeklyProductionTimeData()

  weekBarState.chartOption = {
    update: false,
    tooltip: { trigger: 'axis' },
    legend: {
      data: ['运行', '故障', '停机'],
      textStyle: { color: '#ffffff' },
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '6%',
      bottom: '0%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: weeklyData?.categories || [
        '周一',
        '周二',
        '周三',
        '周四',
        '周五',
        '周六',
        '周日',
      ],
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#aaddff', fontSize: 11 },
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisLabel: { color: '#aaddff' },
      splitLine: {
        lineStyle: { color: 'rgba(0,212,255,0.1)', type: 'dashed' },
      },
    },
    series: [
      {
        name: '运行',
        type: 'bar',
        stack: 'total',
        data: weeklyData?.running || [120, 132, 101, 134, 90, 230, 210],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#00d4ff' },
              { offset: 1, color: 'rgba(0, 212, 255, 0)' },
            ],
          },
        },
      },
      {
        name: '故障',
        type: 'bar',
        stack: 'total',
        data: weeklyData?.fault || [20, 18, 19, 23, 29, 33, 31],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#ff5252' },
              { offset: 1, color: 'rgba(255, 82, 82, 0)' },
            ],
          },
        },
      },
      {
        name: '停机',
        type: 'bar',
        stack: 'total',
        data: weeklyData?.stop || [10, 15, 11, 13, 12, 13, 10],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#666666' },
              { offset: 1, color: 'rgba(102, 102, 102, 0)' },
            ],
          },
        },
      },
    ],
  }
}

processWeekBarOption()

onMounted(() => {
  // 其余初始化
  // 若需要动态刷新，可在此处重新计算周图表
})
</script>

<style lang="less" scoped>
.right-content {
  padding: 10px;

  /* 公共 */
  .data-header {
    display: flex;
    align-items: center;
    height: 42px;
    background: linear-gradient(
      135deg,
      rgba(0, 212, 255, 0.15),
      rgba(0, 212, 255, 0.05)
    );
    backdrop-filter: blur(10px);

    .icon {
      font-size: 45px;
      color: #00d4ff;
      filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
    }

    .title {
      color: #75d1f0;
      font-size: 26px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .data-card-body {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }

  /* ===== 实时生产量 ===== */
  .production-type-grid {
    display: flex;
    justify-content: space-between;
    gap: 10px;
  }

  .type-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.05),
      rgba(0, 212, 255, 0.02)
    );
    border: 1px solid rgba(0, 212, 255, 0.25);
    border-radius: 14px;
    padding: 14px 10px 12px;
    backdrop-filter: blur(8px);
    overflow: hidden;
    transition: all 0.25s ease;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: inherit;
      padding: 1px;
      background: linear-gradient(
        120deg,
        rgba(0, 212, 255, 0.35),
        rgba(0, 212, 255, 0) 60%
      );
      -webkit-mask: linear-gradient(#000 0 0) content-box,
        linear-gradient(#000 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      pointer-events: none;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2);
    }

    .circle {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: radial-gradient(
        circle,
        rgba(0, 212, 255, 0.15) 0%,
        rgba(0, 212, 255, 0.03) 75%
      );
      margin-bottom: 6px;

      i {
        font-size: 26px;
        color: #00d4ff;
        filter: drop-shadow(0 0 6px rgba(0, 212, 255, 0.45));
      }
    }

    .type-name {
      color: #00d4ff;
      font-size: 13px;
      font-weight: 600;
      margin-bottom: 4px;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .type-value-wrapper {
      display: flex;
      align-items: baseline;
      gap: 2px;

      .type-value {
        color: #00d4ff;
        font-size: 22px;
        font-weight: 700;
        text-shadow: 0 1px 4px rgba(0, 212, 255, 0.5);
      }

      .unit {
        color: rgba(0, 212, 255, 0.8);
        font-size: 12px;
      }
    }
  }

  /* ===== 铜箔库存新样式 ===== */
  .inventory-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.06),
      rgba(0, 212, 255, 0.02)
    );
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 14px;
    padding: 12px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 14px rgba(0, 212, 255, 0.08);
  }

  .inv-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .name {
    font-size: 12px;
    color: #00d4ff;
    font-weight: 600;
  }

  .value-wrap {
    display: flex;
    align-items: baseline;
    gap: 2px;

    .value {
      font-size: 20px;
      color: #00d4ff;
      font-weight: 700;
      text-shadow: 0 1px 3px rgba(0, 212, 255, 0.5);
    }

    .unit {
      font-size: 12px;
      color: rgba(0, 212, 255, 0.8);
    }
  }

  .progress {
    width: 100%;
    height: 6px;
    background: rgba(0, 212, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;

    .bar {
      height: 100%;
      background: linear-gradient(90deg, #00d4ff 0%, #0099cc 100%);
    }
  }

  /* ===== 累计生产时长新样式 ===== */
  .time-body {
    gap: 10px;
    align-items: center;
  }

  .total-wrap {
    margin-left: auto;
    display: flex;
    align-items: baseline;
    gap: 4px;

    .total-value {
      font-size: 24px;
      color: #00d4ff;
      font-weight: 700;
    }

    .unit {
      font-size: 12px;
      color: rgba(0, 212, 255, 0.8);
    }
  }

  .pie-container {
    width: 100%;
    position: relative;
    background: radial-gradient(
      circle at center,
      rgba(0, 212, 255, 0.12) 0%,
      rgba(0, 212, 255, 0.02) 70%
    );
    border: 1px solid rgba(0, 212, 255, 0.15);
    border-radius: 12px;
    backdrop-filter: blur(6px);
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    height: 300px;
  }

  .pill-list {
    display: flex;
    gap: 8px;
    justify-content: center;
  }

  .pill {
    display: flex;
    align-items: baseline;
    gap: 3px;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    flex-shrink: 0;
  }

  .bar-container {
    width: 100%;
    height: 200px;
    position: relative;
    background: linear-gradient(
      145deg,
      rgba(0, 212, 255, 0.05),
      rgba(0, 212, 255, 0.02)
    );
    border: 1px solid rgba(0, 212, 255, 0.25);
    border-radius: 8px;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='104' viewBox='0 0 120 104'%3E%3Cdefs%3E%3Cpattern id='hexProd' width='60' height='34.64' patternUnits='userSpaceOnUse'%3E%3Cpath d='M30 0 L60 17.32 L60 51.96 L30 69.28 L0 51.96 L0 17.32 Z' fill='none' stroke='%2300d4ff' stroke-opacity='0.08'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='120' height='104' fill='url(%23hexProd)'/%3E%3C/svg%3E");
      background-size: 120px 104px;
      pointer-events: none;
    }
  }
}
</style>
