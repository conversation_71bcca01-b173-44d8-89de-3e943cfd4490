import { ref, computed, onMounted, onUnmounted } from 'vue';
import { formatTime, getRelativeTime, getGreeting, getTimePeriod } from '@/utils/timeUtils.js';

/**
 * 时间相关的组合式函数
 */
export function useTime() {
  // 当前时间
  const currentTime = ref(new Date());
  let timer = null;

  // 启动时间更新定时器
  const startTimer = () => {
    timer = setInterval(() => {
      currentTime.value = new Date();
    }, 1000);
  };

  // 停止时间更新定时器
  const stopTimer = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  };

  // 格式化当前时间
  const formattedTime = computed(() => {
    return formatTime(currentTime.value);
  });

  // 当前时间的问候语
  const greeting = computed(() => {
    return getGreeting(currentTime.value);
  });

  // 当前时间段
  const timePeriod = computed(() => {
    return getTimePeriod(currentTime.value);
  });

  // 组件挂载时启动定时器
  onMounted(() => {
    startTimer();
  });

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stopTimer();
  });

  return {
    currentTime,
    formattedTime,
    greeting,
    timePeriod,
    startTimer,
    stopTimer
  };
}

/**
 * 倒计时组合式函数
 * @param {Date|string|number} targetDate - 目标时间
 */
export function useCountdown(targetDate) {
  const timeLeft = ref({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    isExpired: false
  });

  let timer = null;

  const updateCountdown = () => {
    const now = new Date().getTime();
    const target = new Date(targetDate).getTime();
    const difference = target - now;

    if (difference <= 0) {
      timeLeft.value = {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        isExpired: true
      };
      stopCountdown();
      return;
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    timeLeft.value = {
      days,
      hours,
      minutes,
      seconds,
      isExpired: false
    };
  };

  const startCountdown = () => {
    updateCountdown();
    timer = setInterval(updateCountdown, 1000);
  };

  const stopCountdown = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  };

  // 格式化倒计时显示
  const formattedCountdown = computed(() => {
    const { days, hours, minutes, seconds, isExpired } = timeLeft.value;
    
    if (isExpired) {
      return '已过期';
    }

    const parts = [];
    if (days > 0) parts.push(`${days}天`);
    if (hours > 0) parts.push(`${hours}小时`);
    if (minutes > 0) parts.push(`${minutes}分钟`);
    if (seconds > 0) parts.push(`${seconds}秒`);

    return parts.join(' ') || '0秒';
  });

  onMounted(() => {
    startCountdown();
  });

  onUnmounted(() => {
    stopCountdown();
  });

  return {
    timeLeft,
    formattedCountdown,
    startCountdown,
    stopCountdown
  };
}

/**
 * 相对时间组合式函数
 * @param {Date|string|number} date - 时间
 */
export function useRelativeTime(date) {
  const relativeTime = ref('');
  let timer = null;

  const updateRelativeTime = () => {
    relativeTime.value = getRelativeTime(date);
  };

  const startUpdating = () => {
    updateRelativeTime();
    timer = setInterval(updateRelativeTime, 60000); // 每分钟更新一次
  };

  const stopUpdating = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  };

  onMounted(() => {
    startUpdating();
  });

  onUnmounted(() => {
    stopUpdating();
  });

  return {
    relativeTime,
    startUpdating,
    stopUpdating
  };
}

/**
 * 时间选择器辅助函数
 */
export function useTimeRange() {
  const timeRanges = {
    today: {
      label: '今天',
      getValue: () => {
        const today = new Date();
        const start = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
        return [start, end];
      }
    },
    yesterday: {
      label: '昨天',
      getValue: () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const start = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
        const end = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
        return [start, end];
      }
    },
    thisWeek: {
      label: '本周',
      getValue: () => {
        const now = new Date();
        const monday = new Date(now);
        monday.setDate(now.getDate() - now.getDay() + 1);
        monday.setHours(0, 0, 0, 0);
        
        const sunday = new Date(monday);
        sunday.setDate(monday.getDate() + 6);
        sunday.setHours(23, 59, 59, 999);
        
        return [monday, sunday];
      }
    },
    lastWeek: {
      label: '上周',
      getValue: () => {
        const now = new Date();
        const lastMonday = new Date(now);
        lastMonday.setDate(now.getDate() - now.getDay() - 6);
        lastMonday.setHours(0, 0, 0, 0);
        
        const lastSunday = new Date(lastMonday);
        lastSunday.setDate(lastMonday.getDate() + 6);
        lastSunday.setHours(23, 59, 59, 999);
        
        return [lastMonday, lastSunday];
      }
    },
    thisMonth: {
      label: '本月',
      getValue: () => {
        const now = new Date();
        const start = new Date(now.getFullYear(), now.getMonth(), 1);
        const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        return [start, end];
      }
    },
    lastMonth: {
      label: '上月',
      getValue: () => {
        const now = new Date();
        const start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);
        return [start, end];
      }
    },
    last7Days: {
      label: '最近7天',
      getValue: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(end.getDate() - 6);
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);
        return [start, end];
      }
    },
    last30Days: {
      label: '最近30天',
      getValue: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(end.getDate() - 29);
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);
        return [start, end];
      }
    }
  };

  const getTimeRange = (key) => {
    return timeRanges[key]?.getValue() || null;
  };

  const getTimeRangeOptions = () => {
    return Object.keys(timeRanges).map(key => ({
      value: key,
      label: timeRanges[key].label
    }));
  };

  return {
    timeRanges,
    getTimeRange,
    getTimeRangeOptions
  };
}
